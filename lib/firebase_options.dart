// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members, no_default_cases
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      throw UnsupportedError(
        'DefaultFirebaseOptions have not been configured for web - '
        'you can reconfigure this by running the FlutterFire CLI again.',
      );
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyD9dejUMEQLGnebFdZtYHccuZmQDT-9s_o',
    appId: '1:410367217486:android:8311a954abb68ef7cb181f',
    messagingSenderId: '410367217486',
    projectId: 'dento-c5a3d',
    storageBucket: 'dento-c5a3d.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyBC43n_K_gjlN8jrmuw1MDQ0COD-BXTe1w',
    appId: '1:410367217486:ios:2189d0e70c0e09b0cb181f',
    messagingSenderId: '410367217486',
    projectId: 'dento-c5a3d',
    storageBucket: 'dento-c5a3d.appspot.com',
    androidClientId:
        '410367217486-04i246u967eu7qqfib2t8v2mds4mq9qb.apps.googleusercontent.com',
    iosClientId:
        '410367217486-qroc3m778ofutvv95vqdi3d4bino8p8m.apps.googleusercontent.com',
    iosBundleId: 'in.dentosupport',
  );
}
