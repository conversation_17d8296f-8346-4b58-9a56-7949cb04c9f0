import 'package:dento_support/core/constants/end_point.dart';
import 'package:dento_support/core/services/dio_client.dart';
import 'package:dento_support/features/pending_fees/models/pending_fee.dart';
import 'package:dento_support/features/pending_fees/models/paginated_pending_fees_response.dart';
import 'package:dento_support/features/patients/models/patient.dart';
import 'package:dento_support/features/patients/models/patient_response.dart';

class PendingFeesRepository {
  PendingFeesRepository(this._client);

  final DioClient _client;

  Future<PaginatedPendingFeesResponse?> getPaginatedPendingFees({int page = 1, int limit = 20}) async {
    try {

      final response = await _client.get(
        '${EndPoint.patient}/pending-amounts?page=$page&limit=$limit',
      );

      if (response.data != null) {
        final data = response.data as Map<String, dynamic>;

        if (data.containsKey('pagination')) {
          return PaginatedPendingFeesResponse.fromJson(data);
        } else {
          final items = data['data'] as List<dynamic>;
          final pendingFees = items.map((json) => PendingFee.fromJson(json as Map<String, dynamic>)).toList();

          final totalCount = data['totalCount'] as int? ?? data['total'] as int?;
          final int calculatedTotalPages;
          final bool hasNextPage;

          if (totalCount != null) {
            calculatedTotalPages = (totalCount / limit).ceil();
            hasNextPage = page < calculatedTotalPages;
          } else {
            calculatedTotalPages = page + (pendingFees.length == limit ? 1 : 0);
            hasNextPage = pendingFees.length == limit;
          }

          return PaginatedPendingFeesResponse(
            status: data['status'] as String? ?? 'success',
            data: pendingFees,
            pagination: PaginationMeta(
              currentPage: page,
              totalPages: calculatedTotalPages,
              totalCount: totalCount ?? pendingFees.length,
              limit: limit,
              hasNextPage: hasNextPage,
              hasPrevPage: page > 1,
            ),
          );
        }
      }
      return null;
    } catch (e) {
      rethrow;
    }
  }

  Future<List<PendingFee>> getPendingFees({int page = 1, int limit = 20}) async {
    try {

      final response = await _client.get(
        '${EndPoint.patient}/pending-amounts?page=$page&limit=$limit',
      );

      if (response.data != null && response.data['data'] != null) {
        final data = response.data['data'] as List<dynamic>;
        return data.map((json) => PendingFee.fromJson(json as Map<String, dynamic>)).toList();
      }

      return [];
    } catch (e) {
      rethrow;
    }
  }

  Future<Patient?> getPatientById(int patientId) async {
    try {
      final response = await _client.get('${EndPoint.patient}/$patientId');
      final patientResponse = PatientResponse.fromJson(response.data as Map<String, dynamic>);
      return patientResponse.data;
    } catch (e) {
      return null;
    }
  }
}
