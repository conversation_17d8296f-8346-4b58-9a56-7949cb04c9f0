part of 'pending_fees_cubit.dart';

enum PendingFeesStatus {
  initial,
  loading,
  loadingMore,
  success,
  failure;

  bool get isLoading => this == PendingFeesStatus.loading;
  bool get isLoadingMore => this == PendingFeesStatus.loadingMore;
  bool get isSuccess => this == PendingFeesStatus.success;
  bool get hasError => this == PendingFeesStatus.failure;
}

class PendingFeesState extends Equatable {
  const PendingFeesState({
    this.status = PendingFeesStatus.initial,
    this.pendingFeesList = const [],
    this.errorMessage = '',
    this.currentPage = 1,
    this.totalPages = 1,
    this.totalCount = 0,
    this.hasNextPage = false,
    this.limit = 20,
  });

  final PendingFeesStatus status;
  final List<PendingFee> pendingFeesList;
  final String errorMessage;
  final int currentPage;
  final int totalPages;
  final int totalCount;
  final bool hasNextPage;
  final int limit;

  PendingFeesState copyWith({
    PendingFeesStatus? status,
    List<PendingFee>? pendingFeesList,
    String? errorMessage,
    int? currentPage,
    int? totalPages,
    int? totalCount,
    bool? hasNextPage,
    int? limit,
  }) {
    return PendingFeesState(
      status: status ?? this.status,
      pendingFeesList: pendingFeesList ?? this.pendingFeesList,
      errorMessage: errorMessage ?? this.errorMessage,
      currentPage: currentPage ?? this.currentPage,
      totalPages: totalPages ?? this.totalPages,
      totalCount: totalCount ?? this.totalCount,
      hasNextPage: hasNextPage ?? this.hasNextPage,
      limit: limit ?? this.limit,
    );
  }

  @override
  List<Object> get props => [
        status,
        pendingFeesList,
        errorMessage,
        currentPage,
        totalPages,
        totalCount,
        hasNextPage,
        limit,
      ];
}
