import 'package:dento_support/core/exceptions/app_response_exception.dart';
import 'package:dento_support/core/exceptions/network_exception.dart';
import 'package:dento_support/features/pending_fees/models/pending_fee.dart';
import 'package:dento_support/features/pending_fees/repository/pending_fees_repository.dart';
import 'package:dento_support/features/patients/models/patient.dart';
import 'package:dento_support/features/patients/repository/patient_repository.dart';
import 'package:dento_support/injector.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'pending_fees_state.dart';

class PendingFeesCubit extends Cubit<PendingFeesState> {
  PendingFeesCubit() : super(const PendingFeesState()) {
    _repository = PendingFeesRepository(getIt());
    _patientRepository = PatientRepository(getIt());
  }

  late PendingFeesRepository _repository;
  late PatientRepository _patientRepository;

  Future<void> fetchPendingFees({bool refresh = false}) async {
    if (refresh) {
      emit(state.copyWith(
        status: PendingFeesStatus.loading,
        currentPage: 1,
        pendingFeesList: [],
      ));
    } else {
      emit(state.copyWith(status: PendingFeesStatus.loading));
    }

    try {
      final response = await _repository.getPaginatedPendingFees(
        page: refresh ? 1 : state.currentPage,
        limit: state.limit,
      );

      if (response != null) {
        emit(
          state.copyWith(
            status: PendingFeesStatus.success,
            pendingFeesList: refresh ? response.data : [...state.pendingFeesList, ...response.data],
            currentPage: response.pagination.currentPage,
            totalPages: response.pagination.totalPages,
            totalCount: response.pagination.totalCount,
            hasNextPage: response.pagination.hasNextPage,
          ),
        );
      } else {
        emit(
          state.copyWith(
            status: PendingFeesStatus.success,
            pendingFeesList: [],
            hasNextPage: false,
          ),
        );
      }
    } on AppResponseException catch (exception) {
      emit(
        state.copyWith(
          status: PendingFeesStatus.failure,
          errorMessage: exception.message,
        ),
      );
    } on NetworkException catch (_) {
      emit(
        state.copyWith(
          status: PendingFeesStatus.failure,
          errorMessage: 'No internet connection',
        ),
      );
    } catch (e) {
      emit(
        state.copyWith(
          status: PendingFeesStatus.failure,
          errorMessage: 'Something went wrong, please try again later',
        ),
      );
    }
  }

  Future<void> loadMorePendingFees() async {
    if (!state.hasNextPage || state.status == PendingFeesStatus.loadingMore) {
      return;
    }

    emit(state.copyWith(status: PendingFeesStatus.loadingMore));

    try {
      final response = await _repository.getPaginatedPendingFees(
        page: state.currentPage + 1,
        limit: state.limit,
      );

      if (response != null) {
        emit(
          state.copyWith(
            status: PendingFeesStatus.success,
            pendingFeesList: [...state.pendingFeesList, ...response.data],
            currentPage: response.pagination.currentPage,
            totalPages: response.pagination.totalPages,
            totalCount: response.pagination.totalCount,
            hasNextPage: response.pagination.hasNextPage,
          ),
        );
      }
    } on AppResponseException catch (exception) {
      emit(
        state.copyWith(
          status: PendingFeesStatus.failure,
          errorMessage: exception.message,
        ),
      );
    } on NetworkException catch (_) {
      emit(
        state.copyWith(
          status: PendingFeesStatus.failure,
          errorMessage: 'No internet connection',
        ),
      );
    } catch (e) {
      emit(
        state.copyWith(
          status: PendingFeesStatus.failure,
          errorMessage: 'Something went wrong, please try again later',
        ),
      );
    }
  }

  Future<Patient?> getPatientById(int patientId) async {
    try {
      return await _repository.getPatientById(patientId);
    } catch (e) {
      return null;
    }
  }

  Future<bool> updatePatientActiveStatus(int patientId, bool isActive) async {
    try {
      await _patientRepository.updatePatientActiveStatus(patientId, isActive);
      return true;
    } catch (e) {
      return false;
    }
  }
}
