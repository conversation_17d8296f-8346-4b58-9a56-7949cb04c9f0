import 'package:dento_support/features/pending_fees/models/pending_fee.dart';
import 'package:json_annotation/json_annotation.dart';

part 'paginated_pending_fees_response.g.dart';

@JsonSerializable()
class PaginationMeta {
  const PaginationMeta({
    required this.currentPage,
    required this.totalPages,
    required this.totalCount,
    required this.limit,
    required this.hasNextPage,
    required this.hasPrevPage,
  });

  factory PaginationMeta.fromJson(Map<String, dynamic> json) =>
      _$PaginationMetaFromJson(json);

  final int currentPage;
  final int totalPages;
  final int totalCount;
  final int limit;
  final bool hasNextPage;
  final bool hasPrevPage;

  Map<String, dynamic> toJson() => _$PaginationMetaToJson(this);
}

@JsonSerializable()
class PaginatedPendingFeesResponse {
  const PaginatedPendingFeesResponse({
    required this.status,
    required this.data,
    required this.pagination,
  });

  factory PaginatedPendingFeesResponse.fromJson(Map<String, dynamic> json) =>
      _$PaginatedPendingFeesResponseFromJson(json);

  final String status;
  final List<PendingFee> data;
  final PaginationMeta pagination;

  Map<String, dynamic> toJson() => _$PaginatedPendingFeesResponseToJson(this);
}
