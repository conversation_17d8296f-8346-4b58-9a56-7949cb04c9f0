import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'pending_fee.g.dart';

@JsonSerializable()
class PendingFee extends Equatable {
  const PendingFee({
    required this.id,
    required this.name,
    required this.mobile,
    required this.pendingAmount,
    required this.lastVisitDate,
  });

  factory PendingFee.fromJson(Map<String, dynamic> json) =>
      _$PendingFeeFromJson(json);

  final int id;
  final String name;
  final String mobile;
  final int pendingAmount;
  final String? lastVisitDate;

  Map<String, dynamic> toJson() => _$PendingFeeToJson(this);

  String get formattedLastVisit {
    if (lastVisitDate == null || lastVisitDate!.isEmpty) {
      return 'No previous visit';
    }
    
    try {
      final date = DateTime.parse(lastVisitDate!);
      final months = [
        'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
        'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
      ];
      return 'Last visit: ${months[date.month - 1]} ${date.day}, ${date.year}';
    } catch (e) {
      return 'Last visit: $lastVisitDate';
    }
  }

  String get formattedPendingAmount {
    return '₹$pendingAmount';
  }

  @override
  List<Object?> get props => [
        id,
        name,
        mobile,
        pendingAmount,
        lastVisitDate,
      ];
}
