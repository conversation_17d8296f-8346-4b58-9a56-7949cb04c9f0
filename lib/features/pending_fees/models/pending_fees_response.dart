import 'package:dento_support/features/pending_fees/models/pending_fee.dart';
import 'package:json_annotation/json_annotation.dart';

part 'pending_fees_response.g.dart';

@JsonSerializable()
class PendingFeesResponse {
  const PendingFeesResponse({
    required this.status,
    required this.data,
  });

  factory PendingFeesResponse.fromJson(Map<String, dynamic> json) =>
      _$PendingFeesResponseFromJson(json);

  final String status;
  final List<PendingFee> data;

  Map<String, dynamic> toJson() => _$PendingFeesResponseToJson(this);
}
