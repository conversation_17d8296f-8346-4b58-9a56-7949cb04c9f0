// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pending_fee.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PendingFee _$PendingFeeFromJson(Map<String, dynamic> json) => PendingFee(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      mobile: json['mobile'] as String,
      pendingAmount: (json['pendingAmount'] as num?)?.toInt() ??
                     (json['remainBill'] as num?)?.toInt() ??
                     (json['pendingAmount'] as num?)?.toInt() ?? 0,
      lastVisitDate: json['lastVisitDate'] as String? ??
                     json['lastVisitedDate'] as String?,
    );

Map<String, dynamic> _$PendingFeeToJson(PendingFee instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'mobile': instance.mobile,
      'pendingAmount': instance.pendingAmount,
      'lastVisitDate': instance.lastVisitDate,
    };
