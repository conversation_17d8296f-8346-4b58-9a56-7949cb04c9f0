import 'package:dento_support/core/configs/colors.dart';
import 'package:dento_support/core/constants/app_constant.dart';
import 'package:dento_support/features/pending_fees/models/pending_fee.dart';
import 'package:dento_support/features/pending_fees/cubit/pending_fees_cubit.dart';
import 'package:dento_support/routers/route_utils.dart';
import 'package:dento_support/routers/router.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:url_launcher/url_launcher.dart';

class PendingFeeItemWidget extends StatelessWidget {
  const PendingFeeItemWidget({
    required this.pendingFee, super.key,
  });

  final PendingFee pendingFee;

  @override
  Widget build(BuildContext context) {
    return Dismissible(
      key: Key(pendingFee.id.toString()),
      direction: DismissDirection.endToStart,
      confirmDismiss: (direction) => _showConfirmationDialog(context),
      background: Container(
        alignment: Alignment.centerRight,
        padding: const EdgeInsets.symmetric(horizontal: 20),
        margin: const EdgeInsets.only(bottom: 16),
        decoration: const BoxDecoration(
          color: Colors.red,
          borderRadius: BorderRadius.horizontal(
            right: Radius.circular(20),
          ),
        ),
        child: Image.asset(AppAssets.delete, width: 20, height: 20, color: Colors.white),
      ),
      child: Container(
        margin: const EdgeInsets.only(bottom: 16),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        pendingFee.name,
                        style: const TextStyle(
                          fontFamily: 'assets/fonts/Inter-Bold.ttf',
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                          color: AppColor.textColor
                        ),
                      ),
                      const SizedBox(height: 5),
                      Text(
                        pendingFee.formattedLastVisit,
                        style: const TextStyle(
                          fontFamily: 'assets/fonts/Inter-Regular.ttf',
                          // fontWeight: FontWeight.w500,
                          fontSize: 12,
                          color: Color(0xFF9CA3AF),
                        ),
                      ),
                    ],
                  ),
                ),
                Text(
                  pendingFee.formattedPendingAmount,
                  style: const TextStyle(
                    fontFamily: 'assets/fonts/Inter-Bold.ttf',
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                    color: Color(0xFFFF4D4F),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 10),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                GestureDetector(
                  onTap: () => _makePhoneCall(pendingFee.mobile),
                  child: Row(
                    children: [
                      Image.asset(AppAssets.call,
                          width: 15, height: 15, color: AppColor.primaryColor),
                      const SizedBox(width: 5),
                      Text(
                        '+91 ${pendingFee.mobile}',
                        style: const TextStyle(
                          fontFamily: AppFont.inter,
                          fontWeight: FontWeight.w500,
                          fontSize: 14,
                          color: AppColor.textColor,
                        ),
                      ),
                    ],
                  ),
                ),
                GestureDetector(
                  onTap: () => _navigateToPatientDetails(context),
                  child: const Row(
                    children: [
                      Text(
                        'See More',
                        style: TextStyle(
                          fontFamily: 'assets/fonts/Inter-Regular.ttf',
                          fontWeight: FontWeight.w400,
                          fontSize: 14,
                          color: AppColor.primaryColor,
                        ),
                      ),
                      SizedBox(width: 4),
                      Icon(
                        Icons.arrow_forward_ios,
                        size: 12,
                        color: AppColor.primaryColor,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _makePhoneCall(String phoneNumber) async {
    final Uri phoneUri = Uri(scheme: 'tel', path: phoneNumber);
    if (await canLaunchUrl(phoneUri)) {
      await launchUrl(phoneUri);
    }
  }

  Future<void> _navigateToPatientDetails(BuildContext context) async {
    try {
      final patient = await context.read<PendingFeesCubit>().getPatientById(pendingFee.id);

      if (patient != null && context.mounted) {
        await context.push(
          AppPage.patient.toPath,
          extra: PatientExtraNew(
            patient: patient,
            initialTabIndex: 0,
          ),
        );

        if (context.mounted) {
          await context.read<PendingFeesCubit>().fetchPendingFees(refresh: true);
        }
      } else if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Unable to load patient details'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Error loading patient details'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
  Future<bool?> _showConfirmationDialog(BuildContext context) async {
    return showCupertinoModalPopup<bool>(
      context: context,
      builder: (BuildContext dialogContext) => CupertinoAlertDialog(
        title: const Text('Deactivate Patient'),
        content: Text(
          'Are you sure you want to deactivate ${pendingFee.name}? This will remove them from the pending fees list.',
        ),
        actions: <CupertinoDialogAction>[
          CupertinoDialogAction(
            isDefaultAction: true,
            onPressed: () {
              Navigator.pop(dialogContext, false);
            },
            child: const Text('Cancel'),
          ),
          CupertinoDialogAction(
            isDestructiveAction: true,
            onPressed: () async {
              Navigator.pop(dialogContext, true);
              await _deactivatePatient(context);
            },
            child: const Text('Deactivate'),
          ),
        ],
      ),
    );
  }

  Future<void> _deactivatePatient(BuildContext context) async {
    try {
      final success = await context.read<PendingFeesCubit>().updatePatientActiveStatus(
        pendingFee.id,
        false,
      );

      if (context.mounted) {
        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('patient deactivated successfully'),
              duration: Duration(seconds: 2),
            ),
          );
          await context.read<PendingFeesCubit>().fetchPendingFees(refresh: true);
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to deactivate patient. Please try again.'),
              backgroundColor: Colors.red,
              duration: Duration(seconds: 2),
            ),
          );
        }
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('An error occurred. Please try again.'),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 2),
          ),
        );
      }
    }
  }

}
