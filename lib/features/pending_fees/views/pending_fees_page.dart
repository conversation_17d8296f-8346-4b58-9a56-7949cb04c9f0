import 'package:dento_support/core/configs/colors.dart';
import 'package:dento_support/features/pending_fees/cubit/pending_fees_cubit.dart';
import 'package:dento_support/features/pending_fees/widgets/pending_fee_item_widget.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class PendingFeesPage extends StatelessWidget {
  const PendingFeesPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => PendingFeesCubit()..fetchPendingFees(),
      child: const PendingFeesView(),
    );
  }
}

class PendingFeesView extends StatefulWidget {
  const PendingFeesView({super.key});

  @override
  State<PendingFeesView> createState() => _PendingFeesViewState();
}

class _PendingFeesViewState extends State<PendingFeesView> {
  late ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    final cubit = context.read<PendingFeesCubit>();
    if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent - 100) {
      cubit.loadMorePendingFees();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        surfaceTintColor: Colors.white,
        elevation: 0,
        leading: Padding(
          padding: const EdgeInsets.only(left: 24),
          child: IconButton(
            splashColor: Colors.transparent,
            highlightColor: Colors.transparent,
            icon: const Icon(
              Icons.arrow_back_ios,
              color: AppColor.primaryColor,
              size: 20,
            ),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ),
        title: const Text(
          'Pending Fees',
          style: TextStyle(
            fontFamily: 'assets/fonts/Inter-Bold.ttf',
            fontWeight: FontWeight.w700,
            fontSize: 18,
            color: AppColor.primaryColor,
          ),
        ),
        centerTitle: true,
      ),
      body: BlocListener<PendingFeesCubit, PendingFeesState>(
        listener: (context, state) {
          if (state.status == PendingFeesStatus.failure && state.pendingFeesList.isNotEmpty) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.errorMessage.isEmpty
                    ? 'Failed to load more data'
                    : state.errorMessage),
                action: SnackBarAction(
                  label: 'Retry',
                  onPressed: () => context.read<PendingFeesCubit>().loadMorePendingFees(),
                ),
              ),
            );
          }
        },
        child: BlocBuilder<PendingFeesCubit, PendingFeesState>(
          builder: (context, state) {
          if (state.status == PendingFeesStatus.loading) {
            return const Center(
              child: RefreshProgressIndicator(),
            );
          }

          if (state.status == PendingFeesStatus.failure) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Colors.grey,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    state.errorMessage.isEmpty 
                        ? 'Something went wrong'
                        : state.errorMessage,
                    style: const TextStyle(
                      fontFamily: AppFont.inter,
                      fontSize: 16,
                      color: Colors.grey,
                    ),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => context.read<PendingFeesCubit>().fetchPendingFees(refresh: true),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColor.primaryColor,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('Retry'),
                  ),
                ],
              ),
            );
          }

          if (state.pendingFeesList.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Image.asset(
                    AppAssets.emptySheetPng,
                    height: 166,
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'No pending fees found',
                    style: TextStyle(
                      fontFamily: AppFont.inter,
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF1A202C),
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'All patients have cleared their payments',
                    style: TextStyle(
                      fontFamily: AppFont.inter,
                      fontSize: 14,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            );
          }

          return ListView.builder(
            controller: _scrollController,
            padding: const EdgeInsets.all(16),
            itemCount: state.pendingFeesList.length + (state.hasNextPage ? 1 : 0),
            itemBuilder: (context, index) {
              if (index == state.pendingFeesList.length) {
                return Container(
                  padding: const EdgeInsets.all(16),
                  alignment: Alignment.center,
                  child: state.status == PendingFeesStatus.loadingMore
                      ? const CircularProgressIndicator()
                      : const SizedBox.shrink(),
                );
              }

              final pendingFee = state.pendingFeesList[index];
              return PendingFeeItemWidget(
                pendingFee: pendingFee,
              );
            },
          );
        },
        ),
      ),
    );
  }
}
