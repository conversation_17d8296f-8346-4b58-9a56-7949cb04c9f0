import 'dart:async';
import 'dart:io';

import 'package:dento_support/core/constants/end_point.dart';
import 'package:dento_support/core/services/dio_client.dart';
import 'package:dento_support/core/services/local_storage_service.dart';
import 'package:dento_support/features/app/domain/entities/user.dart';
import 'package:dento_support/injector.dart';
import 'package:dio/dio.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:share_plus/share_plus.dart';
import 'package:url_launcher/url_launcher.dart';

part 'app_event.dart';
part 'app_state.dart';

class AppBloc extends Bloc<AppEvent, AppState> {
  AppBloc()
      : super(
          getIt<LocalStorageService>().isLogin
              ? AppState.authenticated(getIt<LocalStorageService>().user)
              : const AppState.unauthenticated(),
        ) {
    on<AppLoginRequested>(_onLoginRequested);
    on<AppLogoutRequested>(_onLogoutRequested);
    on<UpdateFcmToken>(_updateFcmTokenToServer);
    on<DownloadDataRequested>(_onDownloadDataRequested);
  }

  FutureOr<void> _onLogoutRequested(
    AppLogoutRequested event,
    Emitter<AppState> emit,
  ) {
    getIt<LocalStorageService>().clear();
    emit(const AppState.unauthenticated());
  }

  FutureOr<void> _onLoginRequested(
    AppLoginRequested event,
    Emitter<AppState> emit,
  ) {
    emit(AppState.authenticated(event.user));
  }

  FutureOr<void> _updateFcmTokenToServer(
    UpdateFcmToken event,
    Emitter<AppState> emit,
  ) async {
    if (getIt<LocalStorageService>().isLogin) {
      try {
        await getIt<DioClient>().patch(
          EndPoint.user,
          data: {
            'FcmToken': getIt<LocalStorageService>().fcmToken,
          },
        );
      } catch (_) {}
    }
  }

  FutureOr<void> _onDownloadDataRequested(
      DownloadDataRequested event,
      Emitter<AppState> emit,
      ) async {
    emit(state.copyWith(downloadStatus: DownloadStatus.loading));

    try {
      // Call API to get download URL
      final response = await getIt<DioClient>().get('/download/${event.type}');

      if (response.data['status'] == 'success') {
        final downloadUrl = response.data['data']['downloadUrl'];
        final fileName = response.data['data']['fileName'];
        final recordCount = response.data['data']['recordCount'] as int;
        final success = await _downloadFile(downloadUrl.toString(), fileName.toString());

        if (success) {
          emit(state.copyWith(
            downloadStatus: DownloadStatus.success,
            downloadMessage: 'File downloaded successfully',
            downloadUrl: downloadUrl.toString(),
            fileName: fileName.toString(),
            recordCount: recordCount,
            filePath: await getDownloadFolderPath(),
          ));
        } else {
          emit(state.copyWith(
            downloadStatus: DownloadStatus.failure,
            downloadMessage: 'Failed to download file',
          ));
        }
      } else {
        emit(state.copyWith(
          downloadStatus: DownloadStatus.failure,
          downloadMessage: 'Download failed',
        ));
      }
    } catch (e) {
      emit(state.copyWith(
        downloadStatus: DownloadStatus.failure,
        downloadMessage: 'Error: ${e.toString()}',
      ));
    }
  }

  Future<String> getDownloadFolderPath() async {
    if (Platform.isAndroid) {
      final directories = await getExternalStorageDirectories(type: StorageDirectory.downloads);
      if (directories != null && directories.isNotEmpty) {
        return directories.first.path;
      }
    } else if (Platform.isIOS) {
      final directory = await getApplicationDocumentsDirectory();
      return directory.path;
    }
    return 'Downloads folder not found!';
  }

  Future<bool> _downloadFile(String url, String fileName) async {
    try {
      String path = await getDownloadFolderPath();

      final filePath = '$path/$fileName';
      print('filePath: $filePath');

      final dio = Dio();
      await dio.download(
        url,
        filePath,
        onReceiveProgress: (received, total) {
          if (total != -1) {
            print('Download progress: ${(received / total * 100).toStringAsFixed(0)}%');
          }
        },
      );
      if(Platform.isIOS) {
        await Share.shareXFiles(
            [XFile(filePath)], text: 'Save or share your file');
      } else {
        // // For Android, we can directly save the file
        // await SharePlus.instance.share(ShareParams(files: [XFile(filePath)], text: 'File Downloaded'));
        // print('File saved to: $filePath');
      }

      return true;
    } catch (e) {
      print('Download error: $e');
      return false;
    }
  }


  Future<bool> deleteUser() async {
    final id = getIt<LocalStorageService>().user.id;
    try {
      await getIt<DioClient>()
          .delete<Map<String, dynamic>>('${EndPoint.user}/$id');
      return Future.value(true);
    } catch (_) {
      return Future.value(false);
    }
  }
}
