part of 'app_bloc.dart';

enum AppStatus { authenticated, unauthenticated }

enum DownloadStatus { initial, loading, success, failure }

class AppState extends Equatable {
  const AppState._({
    required this.status,
    this.user,
    this.downloadStatus = DownloadStatus.initial,
    this.downloadMessage,
    this.downloadUrl,
    this.fileName,
    this.filePath,
    this.recordCount,
  });

  const AppState.authenticated(User user)
      : this._(status: AppStatus.authenticated, user: user);

  const AppState.unauthenticated() : this._(status: AppStatus.unauthenticated);

  final AppStatus status;
  final User? user;
  final DownloadStatus downloadStatus;
  final String? downloadMessage;
  final String? downloadUrl;
  final String? fileName;
  final String? filePath;
  final int? recordCount;

  AppState copyWith({
    AppStatus? status,
    User? user,
    DownloadStatus? downloadStatus,
    String? downloadMessage,
    String? downloadUrl,
    String? filePath,
    String? fileName,
    int? recordCount,
  }) {
    return AppState._(
      status: status ?? this.status,
      user: user ?? this.user,
      downloadStatus: downloadStatus ?? this.downloadStatus,
      downloadMessage: downloadMessage ?? this.downloadMessage,
      downloadUrl: downloadUrl ?? this.downloadUrl,
      fileName: fileName ?? this.fileName,
      filePath: filePath ?? this.filePath,
      recordCount: recordCount ?? this.recordCount,
    );
  }

  @override
  List<Object?> get props => [
        status,
        user,
        downloadStatus,
        downloadMessage,
        downloadUrl,
        fileName,
        filePath,
        recordCount
      ];
}
