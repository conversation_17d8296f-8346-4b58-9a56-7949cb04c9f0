import 'package:dento_support/core/configs/colors.dart';
import 'package:dento_support/core/constants/app_constant.dart';
import 'package:dento_support/core/services/local_storage_service.dart';
import 'package:dento_support/features/app/bloc/app_bloc.dart';
import 'package:dento_support/features/auth/presentation/cubit/user_cubit.dart';
import 'package:dento_support/features/settings/widgets/logout_button_widget.dart';
import 'package:dento_support/features/settings/widgets/setting_item_widget.dart';
import 'package:dento_support/features/settings/widgets/subscription_widget.dart';
import 'package:dento_support/injector.dart';
import 'package:dento_support/routers/route_utils.dart';
import 'package:dento_support/widgets/primary_button.dart';
import 'package:dento_support/widgets/user_detail_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:url_launcher/url_launcher.dart';

class SettingsPage extends StatelessWidget {
  const SettingsPage({super.key});

  Future<void> _launchURLApp(String url) async {
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url));
    } else {
      throw Exception('Could not launch $url');
    }
  }

  Future<void> _launchExternalUrl(String url) async {
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(
        Uri.parse(url),
        mode: LaunchMode.externalApplication,
      );
    } else {
      throw Exception('Could not launch $url');
    }
  }

  void _showDownloadOptionsDialog(BuildContext context) {
    showModalBottomSheet<void>(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return BlocListener<AppBloc, AppState>(
          listener: (context, state) {
            if (state.downloadStatus == DownloadStatus.success) {
              Navigator.pop(context);
              _showDownloadSuccessDialog(context, state);
            } else if (state.downloadStatus == DownloadStatus.failure) {
              Navigator.pop(context);
              _showDownloadErrorDialog(context, state.downloadMessage ?? 'Download failed');
            }
          },
          child: BlocBuilder<AppBloc, AppState>(
            builder: (context, state) {
              return Container(
                height: 220,
                padding: const EdgeInsets.all(20),
                decoration: const BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
                ),
                child: Column(
                  children: [
                    const Text(
                      'Select data to download',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 20),
                    AppPrimaryButton(
                      title: 'Patients with Treatments',
                      backgroundColor: AppColor.primaryColor,
                      onPressed: state.downloadStatus == DownloadStatus.loading
                          ? null
                          : () {
                        context.read<AppBloc>().add(
                          const DownloadDataRequested(type: 'patients-with-treatments'),
                        );
                      },
                    ),
                    const SizedBox(height: 12),
                    AppPrimaryButton(
                      title: 'Patients with Transactions',
                      onPressed: state.downloadStatus == DownloadStatus.loading
                          ? null
                          : () {
                        context.read<AppBloc>().add(
                          const DownloadDataRequested(type: 'patients-with-transactions'),
                        );
                      },
                    ),
                  ],
                ),
              );
            },
          ),
        );
      },
    );
  }

  void _showDownloadSuccessDialog(BuildContext context, AppState state) {
    showCupertinoModalPopup<void>(
      context: context,
      builder: (BuildContext context) {
        return CupertinoAlertDialog(
          title: Text('Download Successful'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('File: ${state.fileName ?? 'Unknown'}'),
              const SizedBox(height: 8),
              Text('The file has been saved to this ${state.filePath}.'),
            ],
          ),
          actions: <CupertinoDialogAction>[
            CupertinoDialogAction(
              /// This parameter indicates this action is the default,
              /// and turns the action's text to bold text.
              isDefaultAction: true,
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('Ok'),
            ),
          ],
        );
      },
    );
  }

  void _showDownloadErrorDialog(BuildContext context, String errorMessage) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Row(
            children: [
              Icon(Icons.error, color: Colors.red),
              SizedBox(width: 8),
              Text('Download Failed'),
            ],
          ),
          content: Text(errorMessage),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
        systemOverlayStyle: const SystemUiOverlayStyle(
          systemNavigationBarColor: Colors.white,
          systemNavigationBarDividerColor: Colors.white,
          systemNavigationBarIconBrightness: Brightness.light,
          statusBarColor: Colors.white,
          statusBarBrightness: Brightness.light,
          statusBarIconBrightness: Brightness.dark,
        ),
      ),
      body: Stack(
        children: [
          SingleChildScrollView(
            child: Column(
              children: [
                const SizedBox(height: 25),
                UserDetailWidget(
                  name: getIt<LocalStorageService>().user.name,
                  phoneNumber: getIt<LocalStorageService>().user.mobile,
                ),
                const SizedBox(height: 25),
                SettingItemWidget(
                  title: 'Edit Profile',
                  onTap: () => context.push(AppPage.editProfile.toPath),
                ),
                SettingItemWidget(
                  title: 'Pending Fees',
                  onTap: () => context.push(AppPage.pendingFees.toPath),
                ),
                SettingItemWidget(
                  title: 'Manage Clinics',
                  onTap: () => context.push(AppPage.manageClinics.toPath),
                ),
                SettingItemWidget(
                  title: 'Terms of Service',
                  onTap: () async => _launchURLApp(AppConstant.termsOfService),
                ),
                SettingItemWidget(
                  title: 'Privacy Policy',
                  onTap: () async =>
                      _launchURLApp(AppConstant.privacyPolicyUrl),
                ),
                SettingItemWidget(
                  title: 'Download Data',
                  onTap: () => _showDownloadOptionsDialog(context),
                ),
                const RequestToDataDeletion(),
                const SizedBox(height: 20),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Text(
                      'Contact us via ',
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 12,
                        color: AppColor.textColor,
                      ),
                    ),
                    InkWell(
                      onTap: () async {
                        final emailLaunchUri = Uri(
                          scheme: 'mailto',
                          path: AppConstant.supportMail,
                        );

                        try {
                          final result = await launchUrl(emailLaunchUri);
                          debugPrint('$result');
                        } catch (_) {
                          debugPrint('Not able to open default mail app');
                        }
                      },
                      child: const Text(
                        'Email',
                        style: TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                          color: AppColor.primaryColor,
                        ),
                      ),
                    ),
                    const Text(
                      ' or via ',
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 12,
                        color: AppColor.textColor,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 10),
                InkWell(
                  onTap: () async => _launchExternalUrl(AppConstant.whatsApp),
                  child: Image.asset(
                    AppAssets.whatsapp,
                    width: MediaQuery.of(context).size.width / 2,
                  ),
                ),
                const SizedBox(height: 136),
              ],
            ),
          ),
          Align(
            alignment: Alignment.bottomCenter,
            child: LogoutButtonWidget(
              onPressed: () {
                showLogoutSheet(context);
              },
            ),
          ),
        ],
      ),
    );
  }

  void showLogoutSheet(BuildContext context) {
    showModalBottomSheet<void>(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return Container(
          height: 300,
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 24),
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
            boxShadow: [
              BoxShadow(
                color: Color.fromRGBO(0, 0, 0, 0.1),
                blurRadius: 4,
              )
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(height: 10),
              Container(
                height: 4,
                width: 28,
                decoration: BoxDecoration(
                  color: const Color(0xFFAAAAAA),
                  borderRadius: BorderRadius.circular(20),
                ),
              ),
              const SizedBox(height: 20),
              const Padding(
                padding: EdgeInsets.symmetric(horizontal: 20),
                child: Text(
                  '''Are you sure you want to Logout of your account dentosupport?''',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontFamily: AppFont.inter,
                    fontWeight: FontWeight.w700,
                    fontSize: 16,
                    color: AppColor.red,
                  ),
                ),
              ),
              const SizedBox(height: 30),
              AppPrimaryButton(
                title: 'Logout',
                backgroundColor: AppColor.red,
                onPressed: () async {
                  context.go(AppPage.signin.toPath);
                  context.read<AppBloc>().add(const AppLogoutRequested());
                },
              ),
              const SizedBox(height: 20),
              AppPrimaryButton(
                title: 'Go Back',
                onPressed: () => Navigator.pop(context),
              ),
            ],
          ),
        );
      },
    );
  }
}

class RequestToDataDeletion extends StatelessWidget {
  const RequestToDataDeletion({super.key});

  @override
  Widget build(BuildContext context) {
    return SettingItemWidget(
      title: 'Request Data Deletion',
      color: AppColor.red,
      onTap: () => showSuccessSheet(context),
    );
  }

  void showSuccessSheet(BuildContext context) {
    showModalBottomSheet<void>(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return Container(
          height: 300,
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 24),
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
            boxShadow: [
              BoxShadow(
                color: Color.fromRGBO(0, 0, 0, 0.1),
                blurRadius: 4,
              )
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(height: 10),
              Container(
                height: 4,
                width: 28,
                decoration: BoxDecoration(
                  color: const Color(0xFFAAAAAA),
                  borderRadius: BorderRadius.circular(20),
                ),
              ),
              const SizedBox(height: 20),
              const Padding(
                padding: EdgeInsets.symmetric(horizontal: 20),
                child: Text(
                  '''Are you sure you want to delete all your data from dentosupport?''',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontFamily: AppFont.inter,
                    fontWeight: FontWeight.w700,
                    fontSize: 16,
                    color: AppColor.red,
                  ),
                ),
              ),
              const SizedBox(height: 15),
              const Padding(
                padding: EdgeInsets.symmetric(horizontal: 40),
                child: Text(
                  '''This action cannot be undone. All patients records and account data will be deleted forever.''',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontFamily: AppFont.inter,
                    fontWeight: FontWeight.w500,
                    fontSize: 10,
                    color: Color(0xFF1A202C),
                  ),
                ),
              ),
              const SizedBox(height: 30),
              AppPrimaryButton(
                title: 'Delete All My Data',
                backgroundColor: AppColor.red,
                onPressed: () async {
                  final success = await context.read<AppBloc>().deleteUser();
                  if (success) {
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      context.pushReplacement(AppPage.signin.toPath);
                      context.read<AppBloc>().add(const AppLogoutRequested());
                    });
                  }
                },
              ),
              const SizedBox(height: 20),
              AppPrimaryButton(
                title: 'Go Back',
                onPressed: () => Navigator.pop(context),
              ),
            ],
          ),
        );
      },
    );
  }
}