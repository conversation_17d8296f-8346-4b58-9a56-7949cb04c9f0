import 'dart:async';

import 'package:dento_support/core/exceptions/app_response_exception.dart';
import 'package:dento_support/core/exceptions/network_exception.dart';
import 'package:dento_support/core/extensions/list_extension.dart';
import 'package:dento_support/core/services/local_storage_service.dart';
import 'package:dento_support/core/utils/date_format_utils.dart';
import 'package:dento_support/features/patients/models/next_schedule.dart';
import 'package:dento_support/features/patients/models/transaction.dart';
import 'package:dento_support/features/patients/models/treatment.dart';
import 'package:dento_support/features/patients/repository/patient_repository.dart';
import 'package:dento_support/injector.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'patient_event.dart';

part 'patient_state.dart';

class PatientBloc extends Bloc<PatientEvent, PatientState> {
  PatientBloc({required PatientRepository patientRepository})
      : _repository = patientRepository,
        super(const PatientState()) {
    on<FetchPatient>(_onFetchedPatient);
    on<UpdatePatientTotals>(_onUpdatePatientTotals);
    on<DeleteTransaction>(_onDeleteTransaction);
    on<EditTransaction>(_onEditTransaction);
  }

  final PatientRepository _repository;

  FutureOr<void> _onFetchedPatient(
    FetchPatient event,
    Emitter<PatientState> emit,
  ) async {
    emit(state.copyWith(status: PatientStatus.initial));

    try {
      final patientResponse = await _repository.getPatientWithId(event.id);
      print("inFetch");
      emit(
        state.copyWith(
          status: PatientStatus.success,
          transactions: patientResponse.data.transactions,
          treatmets: patientResponse.data.treatments,
          receivedPayment: patientResponse.receivedPayment,
          finalPayment: patientResponse.finalPayment,
          discountAmount: patientResponse.discountAmount,
          totalPayment: patientResponse.totalPayment,
          pendingPayment: patientResponse.pendingPayment,
          onProcessTeeth:
              patientResponse.onProcessTeeth.sorted((a, b) => a.compareTo(b)),
          nextSchedule: patientResponse.nextSchedule,
          clearNextSchedule: patientResponse.nextSchedule == null,
        ),
      );
    } on AppResponseException catch (exception) {
      emit(
        state.copyWith(
          status: PatientStatus.failure,
          errorMessage: exception.message,
        ),
      );
    } on NetworkException catch (_) {
      emit(
        state.copyWith(
          errorMessage: 'No internet connection',
          status: PatientStatus.failure,
        ),
      );
    } catch (_) {
      emit(
        state.copyWith(
          status: PatientStatus.failure,
          errorMessage: 'Something went wrong, please try again later',
        ),
      );
    }
  }

  Future<void> addPayment(
      {required String notes,
      required int clinicId,
      required int patientId,
      required int cash,
      required int online}) async {
    final params = AddPaymentHistoryParams(
      notes: notes,
      clinicId: clinicId,
      patientId: patientId,
      cash: cash,
      online: online,
    );

    debugPrint(params.toJson().toString());

    try {
      await _repository.addPaymentHistory(params);

      emit(
        state.copyWith(
          // status: PaymentStatus.success,
          errorMessage: 'Add Payment successfully',
        ),
      );
    } on AppResponseException catch (exception) {
      emit(
        state.copyWith(
          // status: PaymentStatus.failure,
          errorMessage: exception.message,
        ),
      );
    } catch (_) {
      emit(
        state.copyWith(
          // status: PaymentStatus.failure,
          errorMessage: 'Something went wrong, please try again later',
        ),
      );
    }
  }

  Future<void> schedulePatient(int patientId, DateTime dateTime) async {
    final clinicId = getIt<LocalStorageService>().user.clinics.first.id;
    try {
      await _repository.scheduleVisitor(
        patientId: patientId,
        clinicId: clinicId,
        date: dateTime.dateInput,
      );
    } catch (_) {}
  }

  Future<void> reschedulePatient(
    int patientId,
    DateTime newDate,
    DateTime previousDate,
  ) async {
    final clinicId = getIt<LocalStorageService>().user.clinics.first.id;
    try {
      await _repository.rescheduleVisitor(
        patientId: patientId,
        clinicId: clinicId,
        date: newDate.dateInput,
        previousScheduleDate: previousDate.dateInput,
      );
    } catch (_) {}
  }

  FutureOr<void> _onUpdatePatientTotals(
    UpdatePatientTotals event,
    Emitter<PatientState> emit,
  ) async {
    emit(
      state.copyWith(
        totalPayment: event.totalPayment,
        finalPayment: event.finalPayment,
        discountAmount: event.discountAmount,
        pendingPayment: event.pendingPayment,
      ),
    );
  }

  FutureOr<void> _onDeleteTransaction(
    DeleteTransaction event,
    Emitter<PatientState> emit,
  ) async {
    try {
      await _repository.deleteTransaction(
        event.transactionId,
        clinicId: event.clinicId,
        patientId: event.patientId,
      );

      final updatedTransactions = List<Transaction>.from(state.transactions)
        ..removeWhere((transaction) => transaction.id == event.transactionId);

      final newReceivedPayment = updatedTransactions.fold<int>(
        0,
        (sum, transaction) =>
            sum + (transaction.cash ?? 0) + (transaction.online ?? 0),
      );

      final newPendingPayment = state.finalPayment - newReceivedPayment;

      emit(
        state.copyWith(
          transactions: updatedTransactions,
          receivedPayment: newReceivedPayment,
          pendingPayment: newPendingPayment,
          status: PatientStatus.success,
          errorMessage: 'Transaction deleted successfully',
        ),
      );
    } on AppResponseException catch (exception) {
      emit(
        state.copyWith(
          status: PatientStatus.failure,
          errorMessage: exception.message,
        ),
      );
    } catch (_) {
      emit(
        state.copyWith(
          status: PatientStatus.failure,
          errorMessage: 'Failed to delete transaction. Please try again.',
        ),
      );
    }
  }

  FutureOr<void> _onEditTransaction(
    EditTransaction event,
    Emitter<PatientState> emit,
  ) async {
    try {
      final currentTransactionIndex = state.transactions.indexWhere(
        (transaction) => transaction.id == event.transactionId,
      );

      if (currentTransactionIndex == -1) {
        emit(
          state.copyWith(
            status: PatientStatus.failure,
            errorMessage: 'Transaction not found',
          ),
        );
        return;
      }

      final currentTransaction = state.transactions[currentTransactionIndex];

      final params = EditTransactionParams(
        notes: event.notes ?? currentTransaction.notes,
        cash: event.cash ?? currentTransaction.cash,
        online: event.online ?? currentTransaction.online,
        createdAt: event.createdAt,
        clinicId: event.clinicId,
        patientId: event.patientId,
      );

      await _repository.editTransaction(event.transactionId, params);


      final updatedTransactions = List<Transaction>.from(state.transactions);
      final transactionIndex = updatedTransactions.indexWhere(
        (transaction) => transaction.id == event.transactionId,
      );

      if (transactionIndex != -1) {
        final oldTransaction = updatedTransactions[transactionIndex];
        final updatedTransaction = Transaction(
          processedToothNumber: oldTransaction.processedToothNumber,
          id: oldTransaction.id,
          type: oldTransaction.type,
          amount: oldTransaction.amount,
          cash: event.cash ?? oldTransaction.cash,
          online: event.online ?? oldTransaction.online,
          notes: event.notes ?? oldTransaction.notes,
          createdAt: event.createdAt != null ? DateTime.parse(event.createdAt!) : oldTransaction.createdAt,
          patientId: oldTransaction.patientId,
          clinicId: oldTransaction.clinicId,
        );

        updatedTransactions[transactionIndex] = updatedTransaction;

        final newReceivedPayment = updatedTransactions.fold<int>(
          0,
          (sum, transaction) =>
              sum + (transaction.cash ?? 0) + (transaction.online ?? 0),
        );

        final newPendingPayment = state.finalPayment - newReceivedPayment;

        emit(
          state.copyWith(
            transactions: updatedTransactions,
            receivedPayment: newReceivedPayment,
            pendingPayment: newPendingPayment,
            status: PatientStatus.success,
            errorMessage: 'Transaction updated successfully',
          ),
        );
      }
    } on AppResponseException catch (exception) {
      emit(
        state.copyWith(
          status: PatientStatus.failure,
          errorMessage: exception.message,
        ),
      );
    } catch (_) {
      emit(
        state.copyWith(
          status: PatientStatus.failure,
          errorMessage: 'Failed to update transaction. Please try again.',
        ),
      );
    }
  }
}
