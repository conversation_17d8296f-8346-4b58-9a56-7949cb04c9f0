part of 'patient_bloc.dart';

abstract class PatientEvent extends Equatable {
  const PatientEvent();

  @override
  List<Object> get props => [];
}

class FetchPatient extends PatientEvent {
  const FetchPatient(this.id);

  final int id;

  @override
  List<Object> get props => [id];
}

class UpdatePatientTotals extends PatientEvent {
  const UpdatePatientTotals({
    required this.totalPayment,
    required this.finalPayment,
    required this.discountAmount,
    required this.pendingPayment,
  });

  final int totalPayment;
  final int finalPayment;
  final int discountAmount;
  final int pendingPayment;

  @override
  List<Object> get props => [totalPayment, finalPayment, discountAmount, pendingPayment];
}

class DeleteTransaction extends PatientEvent {
  const DeleteTransaction({
    required this.transactionId,
    required this.clinicId,
    required this.patientId,
  });

  final int transactionId;
  final int clinicId;
  final int patientId;

  @override
  List<Object> get props => [transactionId, clinicId, patientId];
}

class EditTransaction extends PatientEvent {
  const EditTransaction({
    required this.transactionId,
    required this.clinicId,
    required this.patientId,
    this.notes,
    this.cash,
    this.online,
    this.createdAt,
  });

  final int transactionId;
  final int clinicId;
  final int patientId;
  final String? notes;
  final int? cash;
  final int? online;
  final String? createdAt;

  @override
  List<Object> get props => [
    transactionId,
    clinicId,
    patientId,
    notes ?? '',
    cash ?? 0,
    online ?? 0,
    createdAt ?? '',
  ];
}
