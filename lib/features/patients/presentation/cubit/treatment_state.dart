part of 'treatment_cubit.dart';

enum TreatmentStatus {
  initial,
  success,
  deleteSuccess,
  discountSuccess,
  editSuccess,
  failure;

  bool get isSuccess => this == TreatmentStatus.success;
  bool get isDeleteSuccess => this == TreatmentStatus.deleteSuccess;
  bool get isDiscountSuccess => this == TreatmentStatus.discountSuccess;
  bool get isEditSuccess => this == TreatmentStatus.editSuccess;
  bool get hasError => this == TreatmentStatus.failure;
}

class TreatmentState extends Equatable {
  const TreatmentState({
    required this.title,
    this.status = TreatmentStatus.initial,
    this.suggestedTreatments = const [],
  required this.treatmentList,
    this.errorMessage = '',
    this.tratmentCost = 0,
  });

  final TreatmentStatus status;
  final String title;
  final List<SuggestedTreatment> suggestedTreatments;
  final List<Treatment> treatmentList;
  final double tratmentCost;
  final String errorMessage;

  TreatmentState copyWith({
    TreatmentStatus? status,
    String? title,
    String? errorMessage,
    List<SuggestedTreatment>? suggestedTreatments,
    List<Treatment>? treatmentList,
    double? tratmentCost,
  }) {
    return TreatmentState(
      status: status ?? this.status,
      title: title ?? this.title,
      errorMessage: errorMessage ?? this.errorMessage,
      suggestedTreatments: suggestedTreatments ?? this.suggestedTreatments,
      treatmentList: treatmentList ?? this.treatmentList,
      tratmentCost: tratmentCost ?? this.tratmentCost,
    );
  }

  @override
  List<Object> get props =>
      [title, suggestedTreatments, status, errorMessage, tratmentCost,treatmentList];
}
