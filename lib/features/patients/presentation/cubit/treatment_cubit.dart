import 'package:dento_support/core/exceptions/app_response_exception.dart';
import 'package:dento_support/core/extensions/list_extension.dart';
import 'package:dento_support/core/services/local_storage_service.dart';
import 'package:dento_support/features/patients/models/treatment.dart';
import 'package:dento_support/features/patients/presentation/bloc/patient_bloc.dart';
import 'package:dento_support/features/patients/repository/patient_repository.dart';
import 'package:dento_support/injector.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'treatment_state.dart';

class TreatmentCubit extends Cubit<TreatmentState> {
  TreatmentCubit(this.patientId, this.treatments, this._patientBloc)
      : super(
          TreatmentState(
            title: 'Add Treatment',
            suggestedTreatments: SuggestedTreatment.make(),
            treatmentList: treatments,
          ),
        ) {
    _repository = PatientRepository(getIt());
  }

  late PatientRepository _repository;
  final int patientId;
  final PatientBloc _patientBloc;

  String _treatmentTitle = '';
  List<String> teethNumber = [];
  List<Treatment> treatments;
  List<String> childTeethNumber = [];
  List<String> upperRightTeethNumber = [];
  List<String> upperLeftTeethNumber = [];
  List<String> lowerLeftTeethNumber = [];
  List<String> lowerRightTeethNumber = [];

  String get teethNumberString => getToothList(teethNumber).join(', ');

  final List<SuggestedTreatment> _suggestedTreatments =
      SuggestedTreatment.make();

  int _calculateTotalPayment(List<Treatment> treatments) {
    return treatments.fold(0, (sum, treatment) => sum + treatment.amount);
  }

  int _calculateFinalPayment(int totalPayment, int discountAmount) {
    return totalPayment - discountAmount;
  }

  int _calculatePendingPayment(int finalPayment, int receivedPayment) {
    return finalPayment - receivedPayment;
  }

  void _updatePatientTotals(List<Treatment> treatments, int discountAmount) {
    final totalPayment = _calculateTotalPayment(treatments);
    final finalPayment = _calculateFinalPayment(totalPayment, discountAmount);
    final currentReceivedPayment = _patientBloc.state.receivedPayment;
    final pendingPayment =
        _calculatePendingPayment(finalPayment, currentReceivedPayment);

    _patientBloc.add(UpdatePatientTotals(
      totalPayment: totalPayment,
      finalPayment: finalPayment,
      discountAmount: discountAmount,
      pendingPayment: pendingPayment,
    ));
  }

  void updatePatientTotalsAfterPayment(int newReceivedPayment) {
    final currentDiscountAmount = _patientBloc.state.discountAmount;
    final totalPayment = _calculateTotalPayment(state.treatmentList);
    final finalPayment =
        _calculateFinalPayment(totalPayment, currentDiscountAmount);
    final pendingPayment =
        _calculatePendingPayment(finalPayment, newReceivedPayment);

    _patientBloc.add(
      UpdatePatientTotals(
        totalPayment: totalPayment,
        finalPayment: finalPayment,
        discountAmount: currentDiscountAmount,
        pendingPayment: pendingPayment,
      ),
    );
  }

  void updateAppTitle(String title) {
    emit(state.copyWith(title: title));
  }

  String treatmentName() => _treatmentTitle;

  void onTreatmentChanged(String treatment) {
    _treatmentTitle = treatment;
    final suggestedTreatments = _suggestedTreatments
        .where(
          (element) =>
              element.name.toLowerCase().contains(treatment.toLowerCase()),
        )
        .toList();

    emit(state.copyWith(suggestedTreatments: suggestedTreatments));
  }

  void onTreatmentCostChanged(String cost) {
    var tCost = 0.0;
    if (cost.isNotEmpty) {
      tCost = double.parse(cost);
    }
    emit(state.copyWith(tratmentCost: tCost));
  }

  Future<void> editPatientDiscount(String discountAmount, String id) async {
    try {
      await _repository.editPatientDiscount(discountAmount, id);
      emit(
        state.copyWith(
          status: TreatmentStatus.discountSuccess,
          errorMessage: 'Discount updated successfully',
        ),
      );

      // Update patient totals with new discount amount
      final newDiscountAmount = double.tryParse(discountAmount)?.round() ?? 0;
      _updatePatientTotals(state.treatmentList, newDiscountAmount);
    } on AppResponseException catch (exception) {
      emit(
        state.copyWith(
          status: TreatmentStatus.failure,
          errorMessage: exception.message,
        ),
      );
    } catch (_) {
      emit(
        state.copyWith(
          status: TreatmentStatus.failure,
          errorMessage: 'Something went wrong, please try again later',
        ),
      );
    }
  }

  Future<void> addTreatment(
      {String? treatmentTitle, String? treatmentCost}) async {
    try {
      final clinicId = getIt<LocalStorageService>().user.clinics.first.id;

      final params = AddTreatmentParams(
        name: treatmentTitle ?? _treatmentTitle,
        amount: double.tryParse((treatmentCost ?? '0').isNotEmpty
                ? (treatmentCost ?? '0')
                : '0') ??
            state.tratmentCost,
        // toothNumber: teethNumber.sorted((a, b) => a.compareTo(b)).join(', '),
        clinicId: clinicId,
        patientId: patientId,
      );
      await _repository.addTreatment(params);

      emit(
        state.copyWith(
          status: TreatmentStatus.success,
          errorMessage: 'Treatment added successfully',
        ),
      );
    } on AppResponseException catch (exception) {
      emit(
        state.copyWith(
          status: TreatmentStatus.failure,
          errorMessage: exception.message,
        ),
      );
    } catch (_) {
      emit(
        state.copyWith(
          status: TreatmentStatus.failure,
          errorMessage: 'Something went wrong, please try again later',
        ),
      );
    }
  }

  Future<void> deleteTreatment({required int id}) async {
    try {
      await _repository.deleteTreatment(id);
      final newTreatmentList = List<Treatment>.from(state.treatmentList)
        ..removeWhere((element) => element.id == id);
      emit(
        state.copyWith(
          treatmentList: newTreatmentList,
          status: TreatmentStatus.deleteSuccess,
          errorMessage: 'Treatment deleted successfully',
        ),
      );

      // Update patient totals after deleting treatment
      final currentDiscountAmount = _patientBloc.state.discountAmount;
      _updatePatientTotals(newTreatmentList, currentDiscountAmount);
    } on AppResponseException catch (exception) {
      emit(
        state.copyWith(
          status: TreatmentStatus.failure,
          errorMessage: exception.message,
        ),
      );
    } catch (_) {
      emit(
        state.copyWith(
          status: TreatmentStatus.failure,
          errorMessage: 'Something went wrong, please try again later',
        ),
      );
    }
  }

  Future<void> editTreatment(
      {required int id, String? name, double? amount}) async {
    try {
      final requestData = <String, dynamic>{};
      if (name != null) requestData['name'] = name;
      if (amount != null) requestData['amount'] = amount;

      await _repository.editTreatment(id, requestData);

      final updatedTreatmentList = state.treatmentList.map((treatment) {
        if (treatment.id == id) {
          return Treatment(
            id: treatment.id,
            name: name ?? treatment.name,
            amount: amount?.round() ?? treatment.amount,
            toothNumber: treatment.toothNumber,
            status: treatment.status,
            patientId: treatment.patientId,
            clinicId: treatment.clinicId,
            createdAt: treatment.createdAt,
          );
        }
        return treatment;
      }).toList();

      emit(
        state.copyWith(
          treatmentList: updatedTreatmentList,
          status: TreatmentStatus.editSuccess,
          errorMessage: 'Treatment updated successfully',
        ),
      );

      // Update patient totals if amount was changed
      if (amount != null) {
        final currentDiscountAmount = _patientBloc.state.discountAmount;
        _updatePatientTotals(updatedTreatmentList, currentDiscountAmount);
      }
    } on AppResponseException catch (exception) {
      emit(
        state.copyWith(
          status: TreatmentStatus.failure,
          errorMessage: exception.message,
        ),
      );
    } catch (_) {
      emit(
        state.copyWith(
          status: TreatmentStatus.failure,
          errorMessage: 'Something went wrong, please try again later',
        ),
      );
    }
  }

  void selectTreatment(String name) {
    final suggestedTreatments = state.suggestedTreatments
        .map(
          (element) => element.name == name
              ? element.copyWith(selected: true)
              : element.copyWith(selected: false),
        )
        .toList();
    _treatmentTitle = name;
    emit(state.copyWith(suggestedTreatments: suggestedTreatments));
  }
}

class SuggestedTreatment extends Equatable {
  const SuggestedTreatment({
    required this.name,
    this.selected = false,
  });

  final String name;
  final bool selected;

  SuggestedTreatment copyWith({
    String? name,
    bool? selected,
  }) {
    return SuggestedTreatment(
      name: name ?? this.name,
      selected: selected ?? this.selected,
    );
  }

  static List<SuggestedTreatment> make() => const [
        SuggestedTreatment(name: 'Consultation'),
        SuggestedTreatment(name: 'Xray'),
        SuggestedTreatment(name: 'Scaling'),
        SuggestedTreatment(name: 'Metal Braces'),
        SuggestedTreatment(name: 'Ceramic Braces'),
        SuggestedTreatment(name: 'Lingual Braces'),
        SuggestedTreatment(name: 'Clear Aligners'),
        SuggestedTreatment(name: 'Complete Denture'),
        SuggestedTreatment(name: 'Removable Partial Denture'),
        SuggestedTreatment(name: 'Cast Partial Denture'),
        SuggestedTreatment(name: 'Crown'),
        SuggestedTreatment(name: 'Bridge'),
        SuggestedTreatment(name: 'Implant'),
        SuggestedTreatment(name: 'Root Canal Treatment'),
        SuggestedTreatment(name: 'Composite Restoration'),
        SuggestedTreatment(name: 'GIC Restoration'),
        SuggestedTreatment(name: 'Silver Amalgam Restoration'),
        SuggestedTreatment(name: 'Post & Core'),
        SuggestedTreatment(name: 'Inlay'),
        SuggestedTreatment(name: 'Onlay'),
        SuggestedTreatment(name: 'Whitening'),
        SuggestedTreatment(name: 'Extraction'),
        SuggestedTreatment(name: 'Disimpaction'),
        SuggestedTreatment(name: 'Minor Surgical Procedure'),
        SuggestedTreatment(name: 'Gingival Flap Surgery'),
        SuggestedTreatment(name: 'Pulpectomy'),
        SuggestedTreatment(name: 'Pulpotomy'),
        SuggestedTreatment(name: 'Restorations'),
        SuggestedTreatment(name: 'Metal Crown'),
      ];

  @override
  List<Object?> get props => [name, selected];
}
