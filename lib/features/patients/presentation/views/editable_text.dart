import 'package:dento_support/core/configs/colors.dart';
import 'package:dento_support/features/patients/presentation/bloc/patient_bloc.dart';
import 'package:dento_support/features/patients/presentation/cubit/treatment_cubit.dart';
import 'package:dento_support/features/patients/presentation/views/new_payment_history_page.dart';
import 'package:dento_support/features/patients/presentation/views/treatment_list_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class EditableTreatmentText extends StatefulWidget {
  const EditableTreatmentText(
      {required this.text,
      required this.onChange,
      super.key,
      this.padding,
      this.style,
      this.treatmentId, required this.patientId});

  final void Function(String? text) onChange;
  final String text;
  final double? padding;
  final TextStyle? style;
  final int? treatmentId;
  final int patientId;

  @override
  State<EditableTreatmentText> createState() => _EditableTreatmentTextState();
}

class _EditableTreatmentTextState extends State<EditableTreatmentText> {
  bool isEditable = false;
  late TextEditingController _controller;
  late String _originalText;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.text);
    String initialValue = widget.text.trim();
    _originalText = initialValue;
  }

  @override
  void didUpdateWidget(covariant EditableTreatmentText oldWidget) {
    // TODO: implement didUpdateWidget
    super.didUpdateWidget(oldWidget);
    if (widget.text != oldWidget.text) {
      String newValue = widget.text.replaceAll('₹', '').trim();
      _originalText = newValue;
      _controller.text = newValue;
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  Future<void> _submitEdit() async {
    final currentValue = _controller.text.trim();
    if (widget.treatmentId != null && _controller.text.trim().isNotEmpty && currentValue != _originalText) {
      await context.read<TreatmentCubit>().editTreatment(
        id: widget.treatmentId!,
        name: _controller.text.trim(),
      );
      context.read<PatientBloc>().add(FetchPatient(widget.patientId));
      _originalText = currentValue;
    }
    setState(() {
      isEditable = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        print("onDouble tap");
        setState(() {
          isEditable = true;
        });
      },
      child: isEditable
          ? Padding(
              padding: const EdgeInsets.symmetric(vertical: 3.9),
              child: TextFormField(
                controller: _controller,
                autofocus: true,
                // maxLines: null,
                decoration: const InputDecoration(
                    hintText: 'Enter Name ',
                    hintStyle: AppFontStyle.style6,
                    border: InputBorder.none,
                    contentPadding: EdgeInsets.symmetric(horizontal: 16)),
                // readOnly: isEditable,
                style: AppFontStyle.style6,
                onChanged: widget.onChange,
                onFieldSubmitted: (value) async {
                  await _submitEdit();
                },
                onTapOutside: (event) async {
                  print("outside");
                  await _submitEdit();
                },
                // style: AppFontStyle.style6,
              ),
            )
          : Padding(
              padding: EdgeInsets.all(widget.padding ?? 16),
              child: Text(
                widget.text,
                style: widget.style ?? AppFontStyle.style6,
              ),
            ),
    );
  }
}

class EditableTreatmentPrice extends StatefulWidget {
  const EditableTreatmentPrice(
      {required this.text,
      required this.onChange,
      super.key,
      this.padding,
      this.style,
      this.treatmentId, required this.patientId});

  final void Function(String? text) onChange;
  final String text;
  final double? padding;
  final TextStyle? style;
  final int? treatmentId;
  final int patientId;

  @override
  State<EditableTreatmentPrice> createState() => _EditableTreatmentPriceState();
}

class _EditableTreatmentPriceState extends State<EditableTreatmentPrice> {
  bool isEditable = false;
  late TextEditingController _controller;
  late String _originalAmount;

  @override
  void initState() {
    super.initState();
    // Remove the currency symbol and extract just the number
    String initialValue = widget.text.replaceAll('₹', '').trim();
    _originalAmount = initialValue;
    _controller = TextEditingController(text: initialValue);
  }

  @override
  void didUpdateWidget(EditableTreatmentPrice oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Update controller and original amount if widget text changes
    if (widget.text != oldWidget.text) {
      String newValue = widget.text.replaceAll('₹', '').trim();
      _originalAmount = newValue;
      _controller.text = newValue;
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  Future<void> _submitEdit() async {
    final currentAmount = _controller.text.trim();

    // Only call API if amount has actually changed and is valid
    if (widget.treatmentId != null &&
        currentAmount.isNotEmpty &&
        currentAmount != _originalAmount) {
      final amount = double.tryParse(currentAmount);
      if (amount != null) {
        await context.read<TreatmentCubit>().editTreatment(
          id: widget.treatmentId!,
          amount: amount,
        );
        // Update original amount after successful API call
        context.read<PatientBloc>().add(FetchPatient(widget.patientId));
        _originalAmount = currentAmount;
      }
    }

    setState(() {
      isEditable = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        print("onDouble tap");
        setState(() {
          isEditable = true;
        });
      },
      child: Container(
        width: 100,
        height: 35,
        decoration: BoxDecoration(
          border: Border.all(color: const Color(0xFFE5E7EB)),
          borderRadius: BorderRadius.circular(8),
          color: Colors.white,
        ),
        alignment: Alignment.centerRight,
        child: isEditable
            ? TextFormField(
                controller: _controller,
                keyboardType: TextInputType.numberWithOptions(decimal: false),
                inputFormatters: [
                  FilteringTextInputFormatter.deny("."),
                  FilteringTextInputFormatter.deny(","),
                  LengthLimitingTextInputFormatter(7),
                  FilteringTextInputFormatter.digitsOnly,
                  NoLeadingZeroFormatter(),
                ],
                autofocus: true,
                textAlign: TextAlign.center,
                decoration: InputDecoration(
                  hintText: 'Enter amount ',
                  hintStyle:
                      AppFontStyle.style4.copyWith(color: AppColor.textColor),
                  border: InputBorder.none,
                ),
                // readOnly: isEditable,
                style: AppFontStyle.style4.copyWith(color: AppColor.textColor),
                onChanged: widget.onChange,
                onFieldSubmitted: (value) async {
                  await _submitEdit();
                },
                onTapOutside: (event) async {
                  print("outside");
                  await _submitEdit();
                },
                // style: AppFontStyle.style6,
              )
            : Padding(
                padding: EdgeInsets.all(widget.padding ?? 16),
                child: Text(
                  widget.text,
                  style: widget.style ?? AppFontStyle.style4,
                ),
              ),
      ),
    );
  }
}
