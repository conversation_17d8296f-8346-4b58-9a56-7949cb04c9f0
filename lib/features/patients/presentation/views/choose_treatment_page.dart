import 'package:dento_support/core/configs/colors.dart';
import 'package:dento_support/features/patients/presentation/cubit/treatment_cubit.dart';
import 'package:dento_support/widgets/primary_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';

class ChooseTreatmentView extends StatefulWidget {
  const ChooseTreatmentView({
    super.key,
    this.onPressed,
  });

  final VoidCallback? onPressed;

  @override
  State<ChooseTreatmentView> createState() => _ChooseTreatmentViewState();
}

class _ChooseTreatmentViewState extends State<ChooseTreatmentView> {
  final _controller = PageController();

  static const _pagePerItem = 30;
  late int _pageCount;
  late int _lastPageItemLength;
  int _selectedIndex = 0;
  final _SearchController = TextEditingController();

  static const aspectRatio = 69 / 61;
  static const gridCount = 3;
  static const horizontalSpace = 25.0;

  static const normalStyle = TextStyle(
    fontFamily: AppFont.inter,
    fontWeight: FontWeight.w600,
    fontSize: 12,
    color: AppColor.textColor,
  );

  static const selectStyle = TextStyle(
    fontFamily: AppFont.inter,
    fontWeight: FontWeight.w600,
    fontSize: 12,
    color: AppColor.primaryColor,
  );

  // Calculate dynamic height based on number of items
  double _calculateGridHeight(int itemCount) {
    if (itemCount == 0) return 0;

    final screenWidth = MediaQuery.of(context).size.width;
    final itemWidth = (screenWidth - horizontalSpace * 2 - 30) /
        gridCount; // 30 for spacing between items
    final itemHeight = itemWidth / aspectRatio;
    final rows = (itemCount / gridCount).ceil();

    return (rows * itemHeight) + ((rows - 1) * 15); // 15 is mainAxisSpacing
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
      floatingActionButton: Padding(
        padding: const EdgeInsets.only(left: 30, right: 30, bottom: 20),
        child: AppPrimaryButton(
          title: 'Next',
          onPressed: widget.onPressed,
        ),
      ),
      body: BlocBuilder<TreatmentCubit, TreatmentState>(
        builder: (context, state) {
          final num = state.suggestedTreatments.length / _pagePerItem;
          _pageCount = num.isInt ? num.toInt() : num.toInt() + 1;

          final reminder =
              state.suggestedTreatments.length.remainder(_pagePerItem);
          _lastPageItemLength = reminder == 0 ? _pagePerItem : reminder;

          // Calculate dynamic height based on actual content
          final dynamicGridHeight =
              _calculateGridHeight(state.suggestedTreatments.length);

          return SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 25),
                ValueListenableBuilder(
                  valueListenable: _SearchController,
                  builder: (context, value, child) {
                    return Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 30),
                      child: TextField(
                        controller: _SearchController,
                        decoration: InputDecoration(
                          hintText: 'Search or add your own treatment',
                          hintStyle: AppFontStyle.hintStyle,
                          contentPadding: EdgeInsets.only(
                              top: _SearchController.text.isNotEmpty ? 15 : 0),
                          suffixIcon: _SearchController.text.isNotEmpty
                              ? IconButton(
                                  icon: Container(
                                    height: 20,
                                    width: 20,
                                    decoration: BoxDecoration(
                                      color: const Color(0xFFDADADA),
                                      borderRadius: BorderRadius.circular(10),
                                    ),
                                    child: const Center(
                                      child: Icon(
                                        Icons.clear_rounded,
                                        color: Colors.white,
                                        size: 17,
                                      ),
                                    ),
                                  ),
                                  onPressed: () {
                                    _SearchController.clear();
                                    context
                                        .read<TreatmentCubit>()
                                        .onTreatmentChanged('');
                                  },
                                )
                              : null,
                        ),
                        textCapitalization: TextCapitalization.words,
                        style: AppFontStyle.textStyle,
                        onChanged:
                            context.read<TreatmentCubit>().onTreatmentChanged,
                      ),
                    );
                  },
                ),
                const SizedBox(height: 30),
                state.suggestedTreatments.isNotEmpty
                    ? Padding(
                        padding: EdgeInsets.symmetric(horizontal: 30),
                        child: Text(
                          'Suggested Treatments',
                          style: TextStyle(
                            fontFamily: AppFont.inter,
                            fontWeight: FontWeight.w600,
                            fontSize: 12,
                            color: AppColor.textColor,
                          ),
                        ),
                      )
                    : SizedBox(),
                const SizedBox(height: 23),

                // Use dynamic height or minimum height for empty state
                if (state.suggestedTreatments.isEmpty) Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(height: 80,),
                      Image.asset('assets/treatments/therapy.png',height: 180,width: 180,),
                      SizedBox(height: 20,),
                      const Text(
                        'Add Customize Treatment',
                        style: TextStyle(
                          fontFamily: AppFont.inter,
                          fontWeight: FontWeight.w400,
                          fontSize: 14,
                          color: AppColor.textColor,
                        ),
                      ),
                    ],
                  ),
                ) else SizedBox(
                        height: dynamicGridHeight,
                        child: PageView.builder(
                          controller: _controller,
                          itemCount: 1,
                          onPageChanged: (index) {
                            setState(() {
                              _selectedIndex = index;
                            });
                          },
                          itemBuilder: (_, pIndex) => GridView.builder(
                            itemCount: state.suggestedTreatments.length,
                            physics: const NeverScrollableScrollPhysics(),
                            gridDelegate:
                                const SliverGridDelegateWithFixedCrossAxisCount(
                              crossAxisCount: gridCount,
                              childAspectRatio: aspectRatio,
                              crossAxisSpacing: 15,
                              mainAxisSpacing: 15,
                            ),
                            shrinkWrap: true,
                            padding: const EdgeInsets.symmetric(
                                horizontal: horizontalSpace),
                            itemBuilder: (context, index) {
                              final suggestedTreatment =
                                  state.suggestedTreatments[index];

                              return InkWell(
                                onTap: () => context
                                    .read<TreatmentCubit>()
                                    .selectTreatment(suggestedTreatment.name),
                                child: DecoratedBox(
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(15),
                                    boxShadow: const [
                                      BoxShadow(
                                        color: Color.fromRGBO(0, 0, 0, 0.1),
                                        blurRadius: 4,
                                      )
                                    ],
                                    border: Border.all(
                                      color: suggestedTreatment.selected
                                          ? AppColor.primaryColor
                                          : const Color.fromRGBO(
                                              198, 198, 198, 0.5),
                                    ),
                                  ),
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      SvgPicture.asset(
                                        'assets/treatments/${suggestedTreatment.name}.svg',
                                        color: suggestedTreatment.selected
                                            ? AppColor.primaryColor
                                            : null,
                                      ),
                                      const SizedBox(height: 5),
                                      Padding(
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 4),
                                        child: Text(
                                          suggestedTreatment.name,
                                          textAlign: TextAlign.center,
                                          style: suggestedTreatment.selected
                                              ? selectStyle
                                              : normalStyle,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              );
                            },
                          ),
                        ),
                      ),
                const SizedBox(height: 100), // Space for floating action button
              ],
            ),
          );
        },
      ),
    );
  }
}

extension NumExtensions on num {
  bool get isInt => (this % 1) == 0;
}
