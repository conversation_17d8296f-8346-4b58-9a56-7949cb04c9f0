import 'dart:developer';
import 'dart:io';

import 'package:dento_support/core/configs/colors.dart';
import 'package:dento_support/core/services/local_storage_service.dart';
import 'package:dento_support/core/utils/date_format_utils.dart';
import 'package:dento_support/features/patients/models/next_schedule.dart';
import 'package:dento_support/features/patients/models/patient.dart';
import 'package:dento_support/features/patients/models/transaction.dart';
import 'package:dento_support/features/patients/presentation/bloc/patient_bloc.dart';
import 'package:dento_support/features/patients/presentation/bloc/patient_list_bloc.dart';
import 'package:dento_support/features/patients/presentation/bloc/search_bloc.dart';
import 'package:dento_support/features/patients/presentation/views/treatment_list_page.dart';
import 'package:dento_support/features/patients/presentation/widgets/edit_payment.dart';
import 'package:dento_support/features/patients/repository/patient_repository.dart';
import 'package:dento_support/injector.dart';
import 'package:dento_support/widgets/schedule_sheet.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';

class NewPaymentHistoryPage extends StatefulWidget {
  const NewPaymentHistoryPage({
    required this.transactions,
    required this.patient,
    super.key,
  });

  final List<Transaction> transactions;
  final Patient patient;

  @override
  State<NewPaymentHistoryPage> createState() => _NewPaymentHistoryPageState();
}

class _NewPaymentHistoryPageState extends State<NewPaymentHistoryPage> {
  bool _showPaymentForm = false;
  bool _isSubmitting = false;
  final Set<int> _deletingTransactionIds = <int>{};

  final TextEditingController _notesController = TextEditingController();
  final TextEditingController _cashController = TextEditingController();
  final TextEditingController _onlineController = TextEditingController();

  final FocusNode _notesFocusNode = FocusNode();
  final FocusNode _cashFocusNode = FocusNode();
  final FocusNode _onlineFocusNode = FocusNode();

  late final PatientRepository _repository;

  @override
  void initState() {
    super.initState();
    _repository = getIt<PatientRepository>();
  }

  @override
  void dispose() {
    _disposeControllers();
    _disposeFocusNodes();
    super.dispose();
  }

  void _disposeControllers() {
    _notesController.dispose();
    _cashController.dispose();
    _onlineController.dispose();
  }

  void _disposeFocusNodes() {
    _notesFocusNode.dispose();
    _cashFocusNode.dispose();
    _onlineFocusNode.dispose();
  }

  String _formatScheduleDate(DateTime date) {
    final dateFormat = DateFormat('dd MMMM, yyyy');
    return dateFormat.format(date);
  }

  void _hidePaymentForm() {
    setState(() {
      _showPaymentForm = false;
    });
    FocusScope.of(context).unfocus();
  }

  void _showPaymentFormDialog() {
    setState(() {
      _showPaymentForm = true;
    });
  }

  Future<void> _submitPayment() async {
    if (_isSubmitting) return;

    final formData = _extractFormData();

    if (_isFormEmpty(formData)) {
      _hidePaymentForm();
      return;
    }

    await _processPaymentSubmission(formData);
  }

  PaymentFormData _extractFormData() {
    return PaymentFormData(
      notes: _notesController.text.trim(),
      cashText: _cashController.text.trim(),
      onlineText: _onlineController.text.trim(),
    );
  }

  bool _isFormEmpty(PaymentFormData formData) {
    return formData.notes.isEmpty &&
        formData.cashText.isEmpty &&
        formData.onlineText.isEmpty;
  }

  Future<void> _processPaymentSubmission(PaymentFormData formData) async {
    _setSubmittingState(true);

    try {
      await _submitPaymentToAPI(formData);
      await _handleSuccessfulSubmission();
    } catch (e) {
      _handleSubmissionError(e);
    } finally {
      _setSubmittingState(false);
    }
  }

  Future<void> _submitPaymentToAPI(PaymentFormData formData) async {
    final clinicId = getIt<LocalStorageService>().user.clinics.first.id;
    final cash = int.tryParse(formData.cashText) ?? 0;
    final online = int.tryParse(formData.onlineText) ?? 0;

    await context.read<PatientBloc>().addPayment(
          notes: formData.notes,
          clinicId: clinicId,
          patientId: widget.patient.id,
          cash: cash,
          online: online,
        );
  }

  Future<void> _handleSuccessfulSubmission() async {
    _clearFormFields();
    _hidePaymentForm();

    if (mounted) {
      context.read<PatientBloc>().add(FetchPatient(widget.patient.id));
      _showSuccessMessage();
    }
  }

  void _handleSubmissionError(dynamic error) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to add payment: ${error.toString()}'),
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  void _setSubmittingState(bool isSubmitting) {
    if (mounted) {
      setState(() {
        _isSubmitting = isSubmitting;
      });
    }
  }

  void _clearFormFields() {
    _notesController.clear();
    _cashController.clear();
    _onlineController.clear();
  }

  void _showSuccessMessage() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Payment added successfully'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<PatientBloc, PatientState>(
      listener: (context, state) {
        if (state.status == PatientStatus.failure) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.errorMessage),
              duration: const Duration(seconds: 2),
            ),
          );
        } else if (state.status == PatientStatus.success &&
            state.errorMessage == 'Transaction deleted successfully') {
          setState(_deletingTransactionIds.clear);
        } else if (state.status == PatientStatus.success &&
            state.errorMessage == 'Transaction updated successfully') {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Transaction updated successfully'),
              duration: Duration(seconds: 2),
            ),
          );
        }
      },
      child: GestureDetector(
        onTap: _hidePaymentForm,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            ..._buildTransactionList(),
            const SizedBox(height: 8),
            _buildAddPaymentButton(),
            _buildPaymentFormSection(),
            const SizedBox(height: 30),
            _buildScheduleAppointmentButton(),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildTransactionList() {
    return widget.transactions
        .where(
            (transaction) => !_deletingTransactionIds.contains(transaction.id))
        .map((transaction) => _buildTransactionCard(transaction))
        .toList();
  }

  Widget _buildAddPaymentButton() {
    if (_showPaymentForm) return const SizedBox.shrink();

    return GestureDetector(
      onTap: _showPaymentFormDialog,
      child: Column(
        children: [
          const Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.add,
                color: AppColor.primaryColor,
                size: 20,
              ),
              SizedBox(width: 4),
              Text(
                'Add Payment History',
                style: TextStyle(
                  fontFamily: 'assets/fonts/Inter-Regular.ttf',
                  fontWeight: FontWeight.w400,
                  fontSize: 14,
                  color: AppColor.primaryColor,
                ),
              ),
            ],
          ),
          Container(
            height: 1,
            width: 170,
            color: AppColor.primaryColor,
            margin: const EdgeInsets.symmetric(vertical: 8),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentFormSection() {
    if (!_showPaymentForm) return const SizedBox.shrink();

    return Stack(
      children: [
        _buildPaymentForm(),
        // if (_isSubmitting) _buildLoadingOverlay(),
      ],
    );
  }

  Widget _buildScheduleAppointmentButton() {
    return BlocBuilder<PatientBloc, PatientState>(
      builder: (context, state) {
        return Column(
          children: [
            SizedBox(
              height: 48,
              child: ElevatedButton.icon(
                onPressed: _handleScheduleAppointment,
                icon: state.nextSchedule != null
                    ? const Icon(
                  Icons.edit,
                  color: AppColor.primaryColor,
                )
                    : Image.asset(AppAssets.schedule, width: 20, height: 20),
                label: state.nextSchedule != null
                    ? Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      'Next Appointment :  ',
                      style: TextStyle(
                        fontFamily: 'assets/fonts/Inter-SemiBold.ttf',
                        fontWeight: FontWeight.w600,
                        fontSize: Platform.isIOS ? 14 : 16,
                        color: AppColor.primaryColor,
                      ),
                    ),
                    Text(
                      _formatScheduleDate(state.nextSchedule!.date),
                      style: TextStyle(
                        fontFamily: 'assets/fonts/Inter-Medium.ttf',
                        fontWeight: FontWeight.w500,
                        fontSize: Platform.isIOS ? 14 : 16,
                        color: AppColor.textColor,
                      ),
                    ),
                  ],
                )
                    : const Text(
                  'Schedule Appointment',
                  style: TextStyle(
                    fontFamily: AppFont.inter,
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                    color: AppColor.primaryColor,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  padding:  EdgeInsets.symmetric(horizontal: state.nextSchedule != null ? 15 : 40),
                  backgroundColor: Colors.white,
                  side: const BorderSide(
                    color: AppColor.primaryColor,
                    width: 1.5,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  elevation: 0,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Future<void> _handleScheduleAppointment() async {
    final state = context.read<PatientBloc>().state;
    final picked = await _showDatePickerDialog(state.nextSchedule);
    if (picked != null) {
      await _processScheduleAppointment(picked, state.nextSchedule);
    }
  }

  Future<DateTime?> _showDatePickerDialog(NextSchedule? nextSchedule) async {
    final now = DateTime.now();
    final firstDate = DateTime(now.year, now.month, now.day);
    final lastDate = firstDate.add(const Duration(days: 365));

    final clinic = getIt<LocalStorageService>().user.clinics.first;
    final dayOff = clinic.dayOff.map((e) => e.toLowerCase()).toList();

    var initialDate = nextSchedule?.date ?? firstDate;

    while (dayOff.contains(DateFormatUtils.fullWeekDay(initialDate).toLowerCase())) {
      initialDate = initialDate.add(const Duration(days: 1));
      if (initialDate.isAfter(lastDate)) {
        throw Exception('No selectable dates available within the next year.');
      }
    }

    return showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: firstDate,
      lastDate: lastDate,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: AppColor.primaryColor,
            ),
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(
                foregroundColor: AppColor.primaryColor,
              ),
            ),
          ),
          child: child!,
        );
      },
      selectableDayPredicate: (DateTime day) {
        return !dayOff.contains(DateFormatUtils.fullWeekDay(day).toLowerCase());
      },
    );
  }

  Future<void> _processScheduleAppointment(
      DateTime picked, NextSchedule? nextSchedule) async {
    debugPrint('Picked $picked');

    final clinic = getIt<LocalStorageService>().user.clinics.first;
    final dayOff = clinic.dayOff;
    final isDayOff = dayOff.contains(
      DateFormatUtils.fullWeekDay(picked).toLowerCase(),
    );

    if (!mounted) return;

    if (isDayOff) {
      await _handleDayOffScheduling(picked, nextSchedule);
    } else {
      await _scheduleOrReschedulePatient(
          context, widget.patient.id, picked, nextSchedule);
    }
  }

  Future<void> _handleDayOffScheduling(
      DateTime picked, NextSchedule? nextSchedule) async {
    context.read<SearchBloc>().add(const OpenSchedule(schedule: false));

    showScheduleSheet(
      context,
      onSchedule: () async {
        await _scheduleOrReschedulePatient(
            context, widget.patient.id, picked, nextSchedule);
      },
      onCancel: () {
        context.read<SearchBloc>().add(const OpenSchedule(schedule: true));
      },
    );
  }

  Future<void> _scheduleOrReschedulePatient(
    BuildContext context,
    int patientId,
    DateTime picked,
    NextSchedule? nextSchedule,
  ) async {
    try {
      if (nextSchedule != null) {
        await context.read<PatientBloc>().reschedulePatient(
              patientId,
              picked,
              nextSchedule.date,
            );
        _showRescheduleSuccessMessage();
      } else {
        await context.read<PatientBloc>().schedulePatient(patientId, picked);
        _showScheduleSuccessMessage();
      }

      if (!mounted) return;

      context.read<PatientBloc>().add(FetchPatient(patientId));
      _refreshPatientList();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
                'Failed to ${nextSchedule != null ? 'reschedule' : 'schedule'} appointment: ${e.toString()}'),
            duration: const Duration(seconds: 2),
          ),
        );
      }
    }
  }

  void _showScheduleSuccessMessage() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Patient scheduled successfully'),
        duration: Duration(seconds: 1),
      ),
    );
  }

  void _showRescheduleSuccessMessage() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Patient Rescheduled successfully'),
        duration: Duration(seconds: 1),
      ),
    );
  }

  void _refreshPatientList() {
    context.read<PatientListBloc>()
      ..add(const PatientsFetched())
      ..add(const UpdateVisitorCount());
  }

  Widget _buildTransactionCard(Transaction transaction) {
    return Dismissible(
      key: Key(transaction.id.toString()),
      direction: DismissDirection.endToStart,
      background: Container(
        alignment: Alignment.centerRight,
        padding: const EdgeInsets.symmetric(horizontal: 20),
        margin: const EdgeInsets.only(bottom: 16),
        decoration: const BoxDecoration(
          color: Colors.red,
          borderRadius: BorderRadius.horizontal(
            right: Radius.circular(20),
          ),
        ),
        child: Image.asset(AppAssets.delete,
            width: 20, height: 20, color: Colors.white),
      ),
      confirmDismiss: (direction) async {
        final shouldDelete = await _showDeleteConfirmationDialog(transaction);
        if (shouldDelete == true) {
          setState(() {
            _deletingTransactionIds.add(transaction.id);
          });
          await _deleteTransaction(transaction);
        }
        return shouldDelete;
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 16),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.all(Radius.circular(20)),
          boxShadow: [
            BoxShadow(
              color: Colors.black12,
              blurRadius: 4,
              offset: Offset(0, 2),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(14.5),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildTransactionInfo(transaction),
              _buildPaymentAmounts(transaction),
            ],
          ),
        ),
      ),
    );
  }

  BoxDecoration _getCardDecoration() {
    return BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.circular(8),
      boxShadow: [
        BoxShadow(
          color: Colors.black.withOpacity(0.05),
          blurRadius: 4,
          offset: const Offset(0, 2),
        ),
      ],
    );
  }

  Widget _buildTransactionInfo(Transaction transaction) {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          EditablePaymentDate(
            date: transaction.createdAt,
            onChange: (date) {},
            transactionId: transaction.id,
            clinicId: transaction.clinicId,
            patientId: transaction.patientId,
            style: AppFontStyle.textStyle.copyWith(
              fontFamily: 'assets/fonts/Inter-Medium.ttf',
            ),
          ),
          const SizedBox(height: 10),
          EditablePaymentText(
            text: transaction.notes.isNotEmpty ? transaction.notes : 'No Notes',
            onChange: (text) {},
            transactionId: transaction.id,
            clinicId: transaction.clinicId,
            patientId: transaction.patientId,
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentAmounts(Transaction transaction) {
    return IntrinsicHeight(
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildPaymentTag(
            label: 'Cash',
            value: (transaction.cash ?? 0).toString(),
            transaction: transaction,
          ),
          const SizedBox(width: 10),
          _buildPaymentDivider(),
          const SizedBox(width: 10),
          _buildPaymentTag(
            label: 'Online',
            value: (transaction.online ?? 0).toString(),
            transaction: transaction,
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentDivider() {
    return Flexible(
      child: Container(
        width: 1,
        height: double.infinity,
        color: const Color(0xFFE5E7EB),
        margin: const EdgeInsets.symmetric(horizontal: 12),
      ),
    );
  }

  Widget _buildPaymentTag({
    required String label,
    required String value,
    required Transaction transaction,
  }) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w400,
            fontFamily: 'assets/fonts/Inter-Regular.ttf',
            color: AppColor.primaryColor,
          ),
        ),
        const SizedBox(height: 7),
        EditablePaymentPrice(
          text: '₹$value',
          onChange: (newValue) {
            // Handle the change in payment amount
          },
          style: AppFontStyle.style3.copyWith(
            fontFamily: 'assets/fonts/Inter-Regular.ttf',
          ),
          transactionId: transaction.id,
          clinicId: transaction.clinicId,
          patientId: transaction.patientId,
          isCashField: label == 'Cash',
        ),
      ],
    );
  }

  Widget _buildPaymentForm() {
    return GestureDetector(
      onTap: () {}, // Prevent form from closing when tapping inside
      child: Container(
        margin: const EdgeInsets.only(bottom: 16),
        decoration: _getCardDecoration(),
        child: Padding(
          padding: const EdgeInsets.all(14.5),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildFormDateAndNotes(),
              _buildFormPaymentFields(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFormDateAndNotes() {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            DateFormat('dd MMM, yyyy').format(DateTime.now()),
            style: AppFontStyle.textStyle.copyWith(
              fontFamily: 'assets/fonts/Inter-Medium.ttf',
            ),
          ),
          const SizedBox(height: 10),
          _buildNotesField(),
        ],
      ),
    );
  }

  Widget _buildFormPaymentFields() {
    return IntrinsicHeight(
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildPaymentField(
            label: 'Cash',
            controller: _cashController,
            focusNode: _cashFocusNode,
          ),
          const SizedBox(width: 10),
          _buildPaymentDivider(),
          const SizedBox(width: 10),
          _buildPaymentField(
            label: 'Online',
            controller: _onlineController,
            focusNode: _onlineFocusNode,
          ),
        ],
      ),
    );
  }

  Widget _buildNotesField() {
    return GestureDetector(
      onTap: _notesFocusNode.requestFocus,
      child: Container(
        constraints: const BoxConstraints(minHeight: 20),
        child: TextField(
          autofocus: true,
          controller: _notesController,
          focusNode: _notesFocusNode,
          enabled: !_isSubmitting,
          decoration: _getNotesFieldDecoration(),
          style: AppFontStyle.textStyle.copyWith(
            fontFamily: 'assets/fonts/Inter-Regular.ttf',
          ),
          maxLines: null,
          onSubmitted: _handleFieldSubmission,
          onTapOutside: _handleFieldTapOutside,
        ),
      ),
    );
  }

  InputDecoration _getNotesFieldDecoration() {
    return InputDecoration(
      hintText: 'Enter Notes',
      hintStyle: AppFontStyle.textStyle.copyWith(
        fontFamily: 'assets/fonts/Inter-Regular.ttf',
        color: const Color(0xFF969696),
      ),
      border: InputBorder.none,
      contentPadding: EdgeInsets.zero,
      isDense: true,
    );
  }

  Future<void> _handleFieldSubmission(String value) async {
    if (!_isSubmitting) {
      await _submitPayment();
    }
  }

  Future<void> _handleFieldTapOutside(PointerDownEvent event) async {
    _notesFocusNode.unfocus();
    if (!_isSubmitting) {
      await _submitPayment();
    }
  }

  Widget _buildPaymentField({
    required String label,
    required TextEditingController controller,
    required FocusNode focusNode,
  }) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w400,
            fontFamily: 'assets/fonts/Inter-Regular.ttf',
            color: AppColor.primaryColor,
          ),
        ),
        const SizedBox(height: 7),
        GestureDetector(
          onTap: () => focusNode.requestFocus(),
          child: Container(
            constraints: const BoxConstraints(maxWidth: 65, maxHeight: 35),
            decoration: _getPaymentFieldDecoration(),
            alignment: Alignment.center,
            child: TextFormField(
              controller: controller,
              focusNode: focusNode,
              enabled: !_isSubmitting,
              keyboardType:
                  const TextInputType.numberWithOptions(decimal: false),
              inputFormatters: _getPaymentFieldFormatters(),
              textAlign: TextAlign.center,
              decoration: _getPaymentFieldInputDecoration(),
              style: AppFontStyle.style3.copyWith(
                fontFamily: 'assets/fonts/Inter-Regular.ttf',
              ),
              onFieldSubmitted: _handleFieldSubmission,
              onTapOutside: (event) async {
                focusNode.unfocus();
                if (!_isSubmitting) {
                  await _submitPayment();
                }
              },
            ),
          ),
        ),
      ],
    );
  }

  BoxDecoration _getPaymentFieldDecoration() {
    return BoxDecoration(
      border: Border.all(color: const Color(0xFFE5E7EB)),
      borderRadius: BorderRadius.circular(8),
      color: Colors.white,
    );
  }

  List<TextInputFormatter> _getPaymentFieldFormatters() {
    return [
      FilteringTextInputFormatter.deny("."),
      FilteringTextInputFormatter.deny(","),
      LengthLimitingTextInputFormatter(4),
      FilteringTextInputFormatter.digitsOnly,
      NoLeadingZeroFormatter(),
    ];
  }

  InputDecoration _getPaymentFieldInputDecoration() {
    return InputDecoration(
      hintText: '₹0',
      hintStyle: AppFontStyle.style3.copyWith(
        fontFamily: 'assets/fonts/Inter-Regular.ttf',
        color: const Color(0xFF9CA3AF),
      ),
      border: InputBorder.none,
      contentPadding: const EdgeInsets.only(bottom: 12),
    );
  }

  Future<void> _deleteTransaction(Transaction transaction) async {
    try {
      final clinicId = getIt<LocalStorageService>().user.clinics.first.id;

      context.read<PatientBloc>().add(
            DeleteTransaction(
              transactionId: transaction.id,
              clinicId: clinicId,
              patientId: widget.patient.id,
            ),
          );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Transaction deleted successfully'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _deletingTransactionIds.remove(transaction.id);
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to delete transaction: ${e.toString()}'),
            duration: const Duration(seconds: 2),
          ),
        );
      }
    }
  }

  Future<bool?> _showDeleteConfirmationDialog(Transaction transaction) async {
    return showCupertinoModalPopup<bool>(
      context: context,
      builder: (BuildContext dialogContext) => CupertinoAlertDialog(
        title: const Text('Delete Payment'),
        content: const Text(
          'Are you sure?',
        ),
        actions: <CupertinoDialogAction>[
          CupertinoDialogAction(
            isDefaultAction: true,
            onPressed: () {
              Navigator.pop(dialogContext, false);
            },
            child: const Text('Cancel'),
          ),
          CupertinoDialogAction(
            isDestructiveAction: true,
            onPressed: () {
              Navigator.pop(dialogContext, true);
            },
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}

class PaymentFormData {
  const PaymentFormData({
    required this.notes,
    required this.cashText,
    required this.onlineText,
  });

  final String notes;
  final String cashText;
  final String onlineText;
}
