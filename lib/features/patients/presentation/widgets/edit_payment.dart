import 'package:dento_support/core/configs/colors.dart';
import 'package:dento_support/features/patients/presentation/bloc/patient_bloc.dart';
import 'package:dento_support/features/patients/presentation/views/new_payment_history_page.dart';
import 'package:dento_support/features/patients/presentation/views/treatment_list_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';

class EditablePaymentText extends StatefulWidget {
  const EditablePaymentText({
    required this.text,
    required this.onChange,
    super.key,
    this.padding,
    this.style,
    this.treatmentId,
    this.transactionId,
    this.clinicId,
    this.patientId,
  });

  final void Function(String? text) onChange;
  final String text;
  final double? padding;
  final TextStyle? style;
  final int? treatmentId;
  final int? transactionId;
  final int? clinicId;
  final int? patientId;

  @override
  State<EditablePaymentText> createState() => _EditablePaymentTextState();
}

class _EditablePaymentTextState extends State<EditablePaymentText> {
  bool isEditable = false;
  late TextEditingController _controller;
  late String _originalText;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.text);
    if(widget.text == 'No Notes') {
      _controller.text = '';
    }
    String initialValue = widget.text.trim();
    _originalText = initialValue;
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  Future<void> _submitEdit() async {
    final currentValue = _controller.text.trim();

    if (widget.transactionId != null &&
        widget.clinicId != null &&
        widget.patientId != null &&
        currentValue != _originalText) {

      try {
        context.read<PatientBloc>().add(
          EditTransaction(
            transactionId: widget.transactionId!,
            clinicId: widget.clinicId!,
            patientId: widget.patientId!,
            notes: currentValue.isNotEmpty ? currentValue : null,
          ),
        );
        _originalText = currentValue;
      } catch (e) {
        _controller.text = _originalText;
      }
    }

    setState(() {
      isEditable = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        print("onDouble tap");
        setState(() {
          isEditable = true;
        });
      },
      child: isEditable
          ? TextFormField(
            textAlign: TextAlign.start,
            controller: _controller,
            autofocus: true,
            // maxLines: null,
            decoration: InputDecoration(
                hintText: 'Enter Notes',
                hintStyle: AppFontStyle.textStyle.copyWith(fontFamily: 'assets/fonts/Inter-Regular.ttf',color: Color(0xFF9CA3AF)),
                border: InputBorder.none,
              contentPadding: EdgeInsets.only(bottom: 15)
            ),
            // readOnly: isEditable,
            style: AppFontStyle.textStyle.copyWith(fontFamily: 'assets/fonts/Inter-Regular.ttf'),
            onChanged: widget.onChange,
            onFieldSubmitted: (value) async {
              await _submitEdit();
            },
            onTapOutside: (event) async {
              print("outside");
              await _submitEdit();
            },
            // style: AppFontStyle.style6,
          )
          : Text(
            widget.text,
            style: widget.style ?? AppFontStyle.textStyle.copyWith(fontFamily: 'assets/fonts/Inter-Regular.ttf'),
          ),
    );
  }
}

class EditablePaymentPrice extends StatefulWidget {
  const EditablePaymentPrice({
    required this.text,
    required this.onChange,
    super.key,
    this.padding,
    this.style,
    this.treatmentId,
    this.transactionId,
    this.clinicId,
    this.patientId,
    this.isCashField = false,
  });

  final void Function(String? text) onChange;
  final String text;
  final double? padding;
  final TextStyle? style;
  final int? treatmentId;
  final int? transactionId;
  final int? clinicId;
  final int? patientId;
  final bool isCashField;

  @override
  State<EditablePaymentPrice> createState() => _EditablePaymentPriceState();
}

class _EditablePaymentPriceState extends State<EditablePaymentPrice> {
  bool isEditable = false;
  late TextEditingController _controller;
  late String _originalAmount;

  @override
  void initState() {
    super.initState();
    String initialValue = widget.text.replaceAll('₹', '').trim();
    _originalAmount = initialValue;
    _controller = TextEditingController(text: initialValue);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  Future<void> _submitEdit() async {
    final currentAmount = _controller.text.trim();

    if (widget.transactionId != null &&
        widget.clinicId != null &&
        widget.patientId != null &&
        currentAmount != _originalAmount) {

      final amount = int.tryParse(currentAmount);
      if (amount != null && amount >= 0) {
        try {
          context.read<PatientBloc>().add(
            EditTransaction(
              transactionId: widget.transactionId!,
              clinicId: widget.clinicId!,
              patientId: widget.patientId!,
              cash: widget.isCashField ? amount : null,
              online: !widget.isCashField ? amount : null,
            ),
          );
          _originalAmount = currentAmount;
        } catch (e) {
          _controller.text = _originalAmount;
        }
      } else {
        _controller.text = _originalAmount;
      }
    }

    setState(() {
      isEditable = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        print("onDouble tap");
        setState(() {
          isEditable = true;
        });
      },
      child: Container(
        width: 65,
        height: 35,
        decoration: BoxDecoration(
          border: Border.all(color: const Color(0xFFE5E7EB)),
          borderRadius: BorderRadius.circular(8),
          color: Colors.white,
        ),
        alignment: Alignment.center,
        child: isEditable
            ? TextFormField(
                controller: _controller,
                keyboardType: TextInputType.numberWithOptions(decimal: false),
                inputFormatters: [
                  FilteringTextInputFormatter.deny("."),
                  FilteringTextInputFormatter.deny(","),
                  LengthLimitingTextInputFormatter(5),
                  FilteringTextInputFormatter.digitsOnly,
                  NoLeadingZeroFormatter(),
                ],
                autofocus: true,
                textAlign: TextAlign.center,
                decoration: InputDecoration(
                  hintText: '₹0',
                  hintStyle: AppFontStyle.style3.copyWith(fontFamily: 'assets/fonts/Inter-Regular.ttf',color: Color(0xFF9CA3AF)),
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.only(
                    bottom: 12,
                  ),
                ),
                // readOnly: isEditable,
                style: AppFontStyle.style3.copyWith(fontFamily: 'assets/fonts/Inter-Regular.ttf',),
                onChanged: widget.onChange,
                onFieldSubmitted: (value) async {
                  await _submitEdit();
                },
                onTapOutside: (event) async {
                  print("outside");
                  await _submitEdit();
                },
                // style: AppFontStyle.style6,
              )
            : Text(
                widget.text,
                style: widget.style ?? AppFontStyle.style3.copyWith(fontFamily: 'assets/fonts/Inter-Regular.ttf',),
              ),
      ),
    );
  }
}

class EditablePaymentDate extends StatefulWidget {
  const EditablePaymentDate({
    required this.date,
    required this.onChange,
    super.key,
    this.style,
    this.transactionId,
    this.clinicId,
    this.patientId,
  });

  final DateTime date;
  final void Function(DateTime? date) onChange;
  final TextStyle? style;
  final int? transactionId;
  final int? clinicId;
  final int? patientId;

  @override
  State<EditablePaymentDate> createState() => _EditablePaymentDateState();
}

class _EditablePaymentDateState extends State<EditablePaymentDate> {
  late DateTime _originalDate;

  @override
  void initState() {
    super.initState();
    _originalDate = widget.date;
  }

  Future<void> _showDatePicker() async {
    final picked = await showDatePicker(
      context: context,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: AppColor.primaryColor,
            ),
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(
                foregroundColor: AppColor.primaryColor,
              ),
            ),
          ),
          child: child!,
        );
      },
      initialDate: widget.date,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );

    if (picked != null && picked != _originalDate) {
      await _submitDateEdit(picked);
    }
  }

  Future<void> _submitDateEdit(DateTime newDate) async {
    if (widget.transactionId != null &&
        widget.clinicId != null &&
        widget.patientId != null) {
      try {
        context.read<PatientBloc>().add(
          EditTransaction(
            transactionId: widget.transactionId!,
            clinicId: widget.clinicId!,
            patientId: widget.patientId!,
            createdAt: newDate.toIso8601String(),
          ),
        );
        _originalDate = newDate;
        widget.onChange(newDate);
      } catch (e) {
        // Handle error if needed
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _showDatePicker,
      child: Row(
        children: [
          const Icon(Icons.edit, size: 18),
          const SizedBox(width: 5),
          Text(
            DateFormat('dd MMM, yyyy').format(widget.date),
            style: widget.style ?? AppFontStyle.textStyle.copyWith(
              fontFamily: 'assets/fonts/Inter-Medium.ttf',
            ),
          ),
        ],
      ),
    );
  }
}
