// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'treatment.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Treatment _$TreatmentFromJson(Map<String, dynamic> json) => Treatment(
      toothNumber: (json['toothNumber'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      amount: (json['amount'] as num).toInt(),
      status: json['status'] as String,
      patientId: (json['patientId'] as num).toInt(),
      clinicId: (json['clinicId'] as num).toInt(),
      createdAt: DateTime.parse(json['createdAt'] as String),
    );

Map<String, dynamic> _$TreatmentToJson(Treatment instance) => <String, dynamic>{
      'toothNumber': instance.toothNumber,
      'id': instance.id,
      'name': instance.name,
      'amount': instance.amount,
      'status': instance.status,
      'patientId': instance.patientId,
      'clinicId': instance.clinicId,
      'createdAt': instance.createdAt.toIso8601String(),
    };
