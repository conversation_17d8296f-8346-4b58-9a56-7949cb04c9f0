// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'patient_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PatientResponse _$PatientResponseFromJson(Map<String, dynamic> json) =>
    PatientResponse(
      status: json['status'] as String,
      data: Patient.fromJson(json['data'] as Map<String, dynamic>),
      receivedPayment: (json['receivedPayment'] as num?)?.toInt() ?? 0,
      pendingPayment: (json['pendingPayment'] as num?)?.toInt() ?? 0,
      totalPayment: (json['totalPayment'] as num?)?.toInt() ?? 0,
      discountAmount: (json['discountAmount'] as num?)?.toInt() ?? 0,
      finalPayment: (json['finalPayment'] as num?)?.toInt() ?? 0,
      onProcessTeeth: (json['onProcessTeeth'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      nextSchedule: json['nextSchedule'] == null
          ? null
          : NextSchedule.fromJson(json['nextSchedule'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$PatientResponseToJson(PatientResponse instance) =>
    <String, dynamic>{
      'status': instance.status,
      'data': instance.data,
      'receivedPayment': instance.receivedPayment,
      'pendingPayment': instance.pendingPayment,
      'totalPayment': instance.totalPayment,
      'finalPayment': instance.finalPayment,
      'discountAmount': instance.discountAmount,
      'onProcessTeeth': instance.onProcessTeeth,
      'nextSchedule': instance.nextSchedule,
    };
