// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'transaction.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Transaction _$TransactionFromJson(Map<String, dynamic> json) => Transaction(
      processedToothNumber: (json['processedToothNumber'] as List<dynamic>)
          .map((e) => ProcessedToothNumber.fromJson(e as Map<String, dynamic>))
          .toList(),
      id: (json['id'] as num).toInt(),
      type: json['type'] as String?,
      amount: (json['amount'] as num).toInt(),
      cash: (json['cash'] as num?)?.toInt(),
      online: (json['online'] as num?)?.toInt(),
      notes: json['notes'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      patientId: (json['patientId'] as num).toInt(),
      clinicId: (json['clinicId'] as num).toInt(),
    );

Map<String, dynamic> _$TransactionToJson(Transaction instance) =>
    <String, dynamic>{
      'processedToothNumber': instance.processedToothNumber,
      'id': instance.id,
      'type': instance.type,
      'amount': instance.amount,
      'cash': instance.cash,
      'online': instance.online,
      'notes': instance.notes,
      'createdAt': instance.createdAt.toIso8601String(),
      'patientId': instance.patientId,
      'clinicId': instance.clinicId,
    };

ProcessedToothNumber _$ProcessedToothNumberFromJson(
        Map<String, dynamic> json) =>
    ProcessedToothNumber(
      treatment: json['treatment'] as String,
      tooth: (json['tooth'] as List<dynamic>).map((e) => e as String).toList(),
    );

Map<String, dynamic> _$ProcessedToothNumberToJson(
        ProcessedToothNumber instance) =>
    <String, dynamic>{
      'treatment': instance.treatment,
      'tooth': instance.tooth,
    };
