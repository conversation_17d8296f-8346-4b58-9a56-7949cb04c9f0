import 'package:dento_support/features/auth/domain/domain.dart';

class PostLogin {
  const PostLogin(this.repository);

  final AuthRepository repository;

  Future<Login> call(LoginParams params) => repository.verifyOtp(params);
}

class LoginParams {
  const LoginParams({
    required this.mobileno,
    required this.otp,
  });

  final String mobileno;
  final String otp;

  Map<String, dynamic> toJson() => {
        'mobile': mobileno,
        'otp': otp,
      };
}
